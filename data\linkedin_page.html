<!DOCTYPE html>
<html lang="en">
 <head>
  <meta content="d_jobs_guest_search" name="pageKey"/>
  <!-- -->
  <!-- -->
  <meta content="urlType=jserp_custom;emptyResult=false" name="linkedin:pageTag"/>
  <meta content="en_US" name="locale"/>
  <meta data-app-version="2.0.2455" data-browser-id="68641b82-5f96-4c22-86ff-3e8bd79fe7ca" data-call-tree-id="AAY1+gFrEuKkf89XSo9lyQ==" data-dfp-member-lix-treatment="control" data-disable-jsbeacon-pagekey-suffix="false" data-dna-member-lix-treatment="enabled" data-enable-page-view-heartbeat-tracking="" data-human-member-lix-treatment="control" data-member-id="0" data-multiproduct-name="jobs-guest-frontend" data-network-interceptor-lix-value="control" data-page-instance="urn:li:page:d_jobs_guest_search;bl83wO5BS5SLYtKqhHRj2w==" data-recaptcha-v3-integration-lix-value="control" data-service-name="jobs-guest-frontend" data-should-use-full-url-in-pve-path="true" data-sync-apfc-cb-lix-treatment="enabled" data-sync-apfc-headers-lix-treatment="control" id="config"/>
  <link href="https://in.linkedin.com/jobs/computer-science-jobs-hyderabad" rel="canonical"/>
  <!-- -->
  <!-- -->
  <!-- -->
  <!-- -->
  <!-- -->
  <link crossorigin="use-credentials" href="/homepage-guest/manifest.json" rel="manifest"/>
  <link href="https://static.licdn.com/aero-v1/sc/h/al2o9zrvru7aqj8e1x2rzsrca" rel="icon"/>
  <script>
   function getDfd() {let yFn,nFn;const p=new Promise(function(y, n){yFn=y;nFn=n;});p.resolve=yFn;p.reject=nFn;return p;}
          window.lazyloader = getDfd();
          window.tracking = getDfd();
          window.impressionTracking = getDfd();
          window.ingraphTracking = getDfd();
          window.appDetection = getDfd();
          window.pemTracking = getDfd();
  </script>
  <!-- -->
  <title>
   4 Computer Science jobs in Hyderabad
  </title>
  <meta content="Today&amp;#39;s top 4 Computer Science jobs in Hyderabad. Leverage your professional network, and get hired. New Computer Science jobs added daily." name="description"/>
  <meta content="noarchive" name="robots"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <meta content="jobs-guest-frontend" name="litmsProfileName"/>
  <meta content="1" data-counter-metric-endpoint="/jobs-guest/api/ingraphs/counter" data-gauge-metric-endpoint="/jobs-guest/api/ingraphs/gauge" name="clientSideIngraphs"/>
  <meta charset="utf-8"/>
  <meta content="website" property="og:type"/>
  <meta content="4 Computer Science jobs in Hyderabad" property="og:title"/>
  <meta content="https://in.linkedin.com/jobs/computer-science-jobs-hyderabad" property="og:url"/>
  <meta content="Today&amp;#39;s top 4 Computer Science jobs in Hyderabad. Leverage your professional network, and get hired. New Computer Science jobs added daily." property="og:description"/>
  <!-- -->
  <meta content="4 Computer Science jobs in Hyderabad" name="twitter:title"/>
  <meta content="summary_large_image" name="twitter:card"/>
  <meta content="@LinkedIn" name="twitter:site"/>
  <meta content="Today&amp;#39;s top 4 Computer Science jobs in Hyderabad. Leverage your professional network, and get hired. New Computer Science jobs added daily." name="twitter:description"/>
  <meta content="https://www.linkedin.com/jobs/search/?currentJobId=4234905066&amp;distance=25&amp;f_E=2&amp;f_TPR=r86400&amp;geoId=105556991&amp;keywords=computer%20science&amp;origin=JOB_SEARCH_PAGE_JOB_FILTER" property="lnkd:url"/>
  <link href="https://static.licdn.com/aero-v1/sc/h/98lkl1ylk5zxfnyed73gip1zw" rel="stylesheet"/>
  <!-- -->
 </head>
 <body dir="ltr">
  <!-- -->
  <!-- -->
  <!-- -->
  <!-- -->
  <div class="base-serp-page">
   <a class="skip-link btn-md btn-primary absolute z-11 -top-[100vh] focus:top-0" href="#main-content">
    Skip to main content
   </a>
   <header class="base-serp-page__header global-alert-offset">
    <nav aria-label="Primary" class="nav pt-1.5 pb-2 flex items-center justify-between relative flex-nowrap babymamabear:py-1.5 babymamabear:flex-wrap">
     <a class="nav__logo-link link-no-visited-state z-1 mr-auto min-h-[52px] flex items-center babybear:z-0 hover:no-underline focus:no-underline active:no-underline" data-tracking-control-name="public_jobs_nav-header-logo" data-tracking-will-navigate="" href="/?trk=public_jobs_nav-header-logo">
      <span class="sr-only">
       LinkedIn
      </span>
      <!-- -->
      <icon class="block text-color-brand w-[102px] h-[26px]" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/8fkga714vy9b2wk5auqo5reeb" data-test-id="nav-logo">
      </icon>
     </a>
     <section class="search-bar relative flex flex-grow h-[40px] bg-cool-gray-20 min-w-0 max-w-full mx-4 rounded-sm babymamabear:mx-0 babymamabear:mb-1.5 babymamabear:bg-color-transparent babymamabear:w-full babymamabear:flex babymamabear:flex-wrap" data-current-search-type="JOBS">
      <button class="search-bar__placeholder papabear:hidden text-input w-full mt-1.5 !pl-[14px] border-1 border-solid border-color-border-faint rounded-[2px] h-[40px] max-h-[40px] flex items-center overflow-hidden cursor-text" data-tracking-control-name="public_jobs_search-switcher-opener">
       <icon class="text-color-icon w-3 h-3 mr-1" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/397vrsk6op88l4981ji1xe1qt">
       </icon>
       <div class="search-bar__full-placeholder font-sans text-md text-color-text max-w-[calc(100%-40px)] text-left whitespace-nowrap overflow-hidden text-ellipsis">
        <!-- -->
        Computer Science in Hyderabad
        <!-- -->
        <!-- -->
       </div>
       <span class="sr-only">
        Expand search
       </span>
      </button>
      <div class="switcher-tabs__trigger-and-tabs babymamabear:flex">
       <button aria-describedby="switcher-description" aria-expanded="false" class="switcher-tabs__placeholder flex !h-full !py-0 !pl-2 !pr-1.5 border-r-1 border-solid border-r-color-border-faint babymamabear:hidden tab-md papabear:tab-vertical papabear:justify-start cursor-pointer" data-tracking-control-name="public_jobs_switcher-tabs-placeholder">
        <span class="switcher-tabs__placeholder-text m-auto">
        </span>
        <icon class="switcher-tabs__caret-down-filled onload pointer-events-none block my-auto min-h-[24px] min-w-[24px] h-[24px] babymamabear:hidden" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
        </icon>
       </button>
       <div class="hidden" id="switcher-description">
        This button displays the currently selected search type. When expanded it provides a list of search options that will switch the search inputs to match the current selection.
       </div>
       <!-- -->
       <div class="switcher-tabs hidden z-[1] w-auto min-w-[160px] mb-1.5 py-1 absolute top-[48px] left-0 border-solid border-1 border-color-border-faint papabear:container-raised babymamabear:static babymamabear:w-[100vw] babymamabear:h-[48px] babymamabear:p-0 overflow-y-hidden overflow-x-auto md:overflow-x-hidden">
        <ul class="switcher-tabs__list flex flex-1 items-stretch papabear:flex-col" role="tablist">
         <li class="switcher-tabs__tab h-[44px] babymamabear:basis-1/2" role="presentation">
          <button aria-controls="jobs-search-panel" aria-selected="true" class="switcher-tabs__button w-full h-full tab-md papabear:tab-vertical papabear:justify-start cursor-pointer tab-selected" data-switcher-type="JOBS" data-tracking-control-name="public_jobs_switcher-tabs-jobs-search-switcher" id="job-switcher-tab" role="tab">
           Jobs
          </button>
         </li>
         <li class="switcher-tabs__tab h-[44px] babymamabear:basis-1/2" role="presentation">
          <button aria-controls="people-search-panel" aria-selected="false" class="switcher-tabs__button w-full h-full tab-md papabear:tab-vertical papabear:justify-start cursor-pointer" data-switcher-type="PEOPLE" data-tracking-control-name="public_jobs_switcher-tabs-people-search-switcher" id="people-switcher-tab" role="tab">
           People
          </button>
         </li>
         <li class="switcher-tabs__tab h-[44px] babymamabear:basis-1/2" role="presentation">
          <button aria-controls="learning-search-panel" aria-selected="false" class="switcher-tabs__button w-full h-full tab-md papabear:tab-vertical papabear:justify-start cursor-pointer" data-switcher-type="LEARNING" data-tracking-control-name="public_jobs_switcher-tabs-learning-search-switcher" id="learning-switcher-tab" role="tab">
           Learning
          </button>
         </li>
        </ul>
        <button aria-label="Close" class="switcher-tabs__cancel-btn papabear:hidden block w-6 h-6 m-auto text-color-text-low-emphasis" data-tracking-control-name="public_jobs_switcher-tabs-cancel-search-switcher" type="button">
         <icon class="switcher-tabs__cancel-icon block w-3 h-3 m-auto onload" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
         </icon>
        </button>
       </div>
      </div>
      <section aria-labelledby="people-switcher-tab" class="base-search-bar w-full h-full" data-searchbar-type="PEOPLE" id="people-search-panel" role="tabpanel">
       <form action="/pub/dir" class="base-search-bar__form w-full flex babymamabear:mx-mobile-container-padding babymamabear:flex-col" data-tracking-control-name="public_jobs_people-search-bar_base-search-bar-form" role="search">
        <section class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 search-input">
         <input aria-label="First Name" autocomplete="on" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_people-search-bar_first-name_dismissable-input" maxlength="500" name="firstName" placeholder="First Name" type="search"/>
         <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_people-search-bar_first-name_dismissable-input-clear" type="button">
          <label class="sr-only">
           Clear text
          </label>
          <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
          </icon>
         </button>
        </section>
        <section class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 search-input">
         <input aria-label="Last Name" autocomplete="on" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_people-search-bar_last-name_dismissable-input" maxlength="500" name="lastName" placeholder="Last Name" type="search"/>
         <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_people-search-bar_last-name_dismissable-input-clear" type="button">
          <label class="sr-only">
           Clear text
          </label>
          <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
          </icon>
         </button>
        </section>
        <input name="trk" type="hidden" value="public_jobs_people-search-bar_search-submit"/>
        <button aria-label="Search" class="base-search-bar__submit-btn block basis-[40px] flex-shrink-0 cursor-pointer babymamabear:invisible babymamabear:ml-[-9999px] babymamabear:w-[1px] babymamabear:h-[1px]" data-tracking-control-name="public_jobs_people-search-bar_base-search-bar-search-submit" type="submit">
         <icon class="base-search-bar__search-icon onload mx-auto" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cb5bsr4tsn2r4sjg9e3ls4tjl">
         </icon>
        </button>
       </form>
      </section>
      <section aria-labelledby="job-switcher-tab" class="base-search-bar w-full h-full" data-searchbar-type="JOBS" id="jobs-search-panel" role="tabpanel">
       <form action="/jobs/search" class="base-search-bar__form w-full flex babymamabear:mx-mobile-container-padding babymamabear:flex-col" data-tracking-control-name="public_jobs_jobs-search-bar_base-search-bar-form" role="search">
        <code id="i18n_aria_live_text_no-suggestions" style="display: none">
         <!--"No suggestions found"-->
        </code>
        <code id="i18n_aria_live_text_one-suggestion" style="display: none">
         <!--"One Suggestion. Use up and down keys to navigate"-->
        </code>
        <code id="i18n_aria_live_text_multiple-suggestions" style="display: none">
         <!--"Multiple Suggestions. Use up and down keys to navigate"-->
        </code>
        <section class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 typeahead-input keywords-typeahead-input text-input">
         <input aria-autocomplete="list" aria-controls="job-search-bar-keywords-typeahead-list" aria-haspopup="listbox" aria-label="Search job titles or companies" autocomplete="off" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_dismissable-input" id="job-search-bar-keywords" maxlength="500" name="keywords" placeholder="Search job titles or companies" role="combobox" type="search" value="Computer Science"/>
         <!-- -->
         <div class="typeahead-input__dropdown container-lined absolute top-[calc(100%+3px)] left-0 w-full rounded-b-md rounded-t-none z-[10] overflow-hidden max-w-none babybear:min-w-full babybear:bottom-0 babybear:overflow-y-auto">
          <template class="typeahead-item-template">
           <li class="typeahead-input__dropdown-item py-1.5 px-2 hover:cursor-pointer hover:bg-color-surface-new-hover hover:border-y-2 hover:border-solid hover:border-color-container-primary" role="option">
            <span class="typeahead-input__dropdown-text font-sans text-sm font-bold text-color-text">
            </span>
           </li>
          </template>
          <ul class="typeahead-input__dropdown-list w-full" id="job-search-bar-keywords-typeahead-list" role="listbox">
          </ul>
         </div>
         <!-- -->
         <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_dismissable-input-clear" type="button">
          <label class="sr-only">
           Clear text
          </label>
          <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
          </icon>
         </button>
        </section>
        <!-- -->
        <code id="i18n_aria_live_text_no-suggestions" style="display: none">
         <!--"No suggestions found"-->
        </code>
        <code id="i18n_aria_live_text_one-suggestion" style="display: none">
         <!--"One Suggestion. Use up and down keys to navigate"-->
        </code>
        <code id="i18n_aria_live_text_multiple-suggestions" style="display: none">
         <!--"Multiple Suggestions. Use up and down keys to navigate"-->
        </code>
        <section class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 typeahead-input location-typeahead-input">
         <input aria-autocomplete="list" aria-controls="job-search-bar-location-typeahead-list" aria-haspopup="listbox" aria-label="Location" autocomplete="off" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_dismissable-input" id="job-search-bar-location" maxlength="500" name="location" placeholder="Location" role="combobox" type="search" value="Hyderabad"/>
         <!-- -->
         <div class="typeahead-input__dropdown container-lined absolute top-[calc(100%+3px)] left-0 w-full rounded-b-md rounded-t-none z-[10] overflow-hidden max-w-none babybear:min-w-full babybear:bottom-0 babybear:overflow-y-auto">
          <template class="typeahead-item-template">
           <li class="typeahead-input__dropdown-item py-1.5 px-2 hover:cursor-pointer hover:bg-color-surface-new-hover hover:border-y-2 hover:border-solid hover:border-color-container-primary" role="option">
            <span class="typeahead-input__dropdown-text font-sans text-sm font-bold text-color-text">
            </span>
           </li>
          </template>
          <ul class="typeahead-input__dropdown-list w-full" id="job-search-bar-location-typeahead-list" role="listbox">
          </ul>
         </div>
         <!-- -->
         <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_dismissable-input-clear" type="button">
          <label class="sr-only">
           Clear text
          </label>
          <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
          </icon>
         </button>
        </section>
        <input name="geoId" type="hidden" value="105556991"/>
        <input name="trk" type="hidden" value="public_jobs_jobs-search-bar_search-submit"/>
        <button aria-label="Search" class="base-search-bar__submit-btn block basis-[40px] flex-shrink-0 cursor-pointer babymamabear:invisible babymamabear:ml-[-9999px] babymamabear:w-[1px] babymamabear:h-[1px]" data-tracking-control-name="public_jobs_jobs-search-bar_base-search-bar-search-submit" type="submit">
         <icon class="base-search-bar__search-icon onload mx-auto" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cb5bsr4tsn2r4sjg9e3ls4tjl">
         </icon>
        </button>
       </form>
      </section>
      <section aria-labelledby="learning-switcher-tab" class="base-search-bar w-full h-full" data-searchbar-type="LEARNING" id="learning-search-panel" role="tabpanel">
       <form action="/learning/search" class="base-search-bar__form w-full flex babymamabear:mx-mobile-container-padding babymamabear:flex-col" data-tracking-control-name="public_jobs_learning-search-bar_base-search-bar-form" role="search">
        <section class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 search-input">
         <input aria-label="Search skills, subjects, or software" autocomplete="on" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_learning-search-bar_keywords_dismissable-input" maxlength="500" name="keywords" placeholder="Search skills, subjects, or software" type="search" value="Computer Science"/>
         <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_learning-search-bar_keywords_dismissable-input-clear" type="button">
          <label class="sr-only">
           Clear text
          </label>
          <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
          </icon>
         </button>
        </section>
        <input class="nav__search-uoo" name="upsellOrderOrigin" type="hidden"/>
        <input name="trk" type="hidden" value="public_jobs_learning-search-bar_search-submit"/>
        <button aria-label="Search" class="base-search-bar__submit-btn block basis-[40px] flex-shrink-0 cursor-pointer babymamabear:invisible babymamabear:ml-[-9999px] babymamabear:w-[1px] babymamabear:h-[1px]" data-tracking-control-name="public_jobs_learning-search-bar_base-search-bar-search-submit" type="submit">
         <icon class="base-search-bar__search-icon onload mx-auto" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cb5bsr4tsn2r4sjg9e3ls4tjl">
         </icon>
        </button>
       </form>
      </section>
      <div aria-live="polite" class="search-bar__live-text sr-only" role="status">
      </div>
     </section>
     <!-- -->
     <div class="nav__cta-container order-3 flex gap-x-1 justify-end min-w-[100px] flex-nowrap flex-shrink-0 babybear:flex-wrap flex-2">
      <!-- -->
      <a class="nav__button-tertiary btn-md btn-tertiary" data-test-live-nav-primary-cta="" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_nav-header-join" data-tracking-will-navigate="" href="https://www.linkedin.com/signup/cold-join?source=jobs_registration&amp;session_redirect=https%3A%2F%2Fwww.linkedin.com%2Fjobs%2Fsearch%2F%3FcurrentJobId%3D4234905066%26distance%3D25%26f_E%3D2%26f_TPR%3Dr86400%26geoId%3D105556991%26keywords%3Dcomputer%2520science%26origin%3DJOB_SEARCH_PAGE_JOB_FILTER&amp;trk=public_jobs_nav-header-join">
       Join now
      </a>
      <a class="nav__button-secondary btn-secondary-emphasis btn-md" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_nav-header-signin" data-tracking-will-navigate="" href="https://www.linkedin.com/login?emailAddress=&amp;fromSignIn=&amp;fromSignIn=true&amp;session_redirect=https%3A%2F%2Fwww.linkedin.com%2Fjobs%2Fsearch%2F%3FcurrentJobId%3D4234905066%26distance%3D25%26f_E%3D2%26f_TPR%3Dr86400%26geoId%3D105556991%26keywords%3Dcomputer%2520science%26origin%3DJOB_SEARCH_PAGE_JOB_FILTER&amp;trk=public_jobs_nav-header-signin">
       Sign in
      </a>
      <!-- -->
     </div>
     <!-- -->
     <!-- -->
    </nav>
   </header>
   <section class="base-serp-page__filters-bar">
    <div class="base-serp-page__filters">
     <div class="search-filters search-filters--carousel">
      <div class="filters filters--desktop">
       <form action="https://www.linkedin.com/jobs/search/" class="filters__form" data-tracking-control-name="public_jobs_filters" id="jserp-filters">
        <input name="keywords" type="hidden" value="Computer Science"/>
        <input name="location" type="hidden" value="Hyderabad"/>
        <input name="geoId" type="hidden" value="105556991"/>
        <ul class="filters__list">
         <!-- -->
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Distance filter. 25 miles (40 km) filter is currently applied. Clicking this button displays all Distance filter options." class="filter-button pill flex items-center !min-h-0 filter-button--selected pill-checked filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_distance" type="button">
             25 miles (40 km)
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <!-- -->
             <div class="filter-values-container">
              <div aria-label="Distance filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="distance-0" name="distance" type="radio" value="0"/>
                <label for="distance-0">
                 Exact location
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="distance-1" name="distance" type="radio" value="5"/>
                <label for="distance-1">
                 5 miles (8 km)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="distance-2" name="distance" type="radio" value="10"/>
                <label for="distance-2">
                 10 miles (16 km)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input checked="" form="jserp-filters" id="distance-3" name="distance" type="radio" value="25"/>
                <label for="distance-3">
                 25 miles (40 km)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="distance-4" name="distance" type="radio" value="50"/>
                <label for="distance-4">
                 50 miles (80 km)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_distance" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Experience level filter. Entry level filter is currently applied. Clicking this button displays all Experience level filter options." class="filter-button pill flex items-center !min-h-0 filter-button--selected pill-checked filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_f_E" type="button">
             Entry level
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <!-- -->
             <div class="filter-values-container">
              <div aria-label="Experience level filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input checked="" form="jserp-filters" id="f_E-0" name="f_E" type="checkbox" value="2"/>
                <label for="f_E-0">
                 Entry level (4)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_E-1" name="f_E" type="checkbox" value="1"/>
                <label for="f_E-1">
                 Internship (3)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_E-2" name="f_E" type="checkbox" value="3"/>
                <label for="f_E-2">
                 Associate (1)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_E-3" name="f_E" type="checkbox" value="4"/>
                <label for="f_E-3">
                 Mid-Senior level (53)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_E-4" name="f_E" type="checkbox" value="5"/>
                <label for="f_E-4">
                 Director (2)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_f_E" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Date posted filter. Past 24 hours filter is currently applied. Clicking this button displays all Date posted filter options." class="filter-button pill flex items-center !min-h-0 filter-button--selected pill-checked filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_f_TPR" type="button">
             Past 24 hours
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <!-- -->
             <div class="filter-values-container">
              <div aria-label="Date posted filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_TPR-0" name="f_TPR" type="radio" value=""/>
                <label for="f_TPR-0">
                 Any time (496)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_TPR-1" name="f_TPR" type="radio" value="r2592000"/>
                <label for="f_TPR-1">
                 Past month (375)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_TPR-2" name="f_TPR" type="radio" value="r604800"/>
                <label for="f_TPR-2">
                 Past week (216)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input checked="" form="jserp-filters" id="f_TPR-3" name="f_TPR" type="radio" value="r86400"/>
                <label for="f_TPR-3">
                 Past 24 hours (4)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_f_TPR" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Company filter. Clicking this button displays all Company filter options." class="filter-button pill flex items-center !min-h-0 filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_f_C" type="button">
             Company
             <!-- -->
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <code id="i18n_aria_live_text_no-suggestions" style="display: none">
              <!--"No suggestions found"-->
             </code>
             <code id="i18n_aria_live_text_one-suggestion" style="display: none">
              <!--"One Suggestion. Use up and down keys to navigate"-->
             </code>
             <code id="i18n_aria_live_text_multiple-suggestions" style="display: none">
              <!--"Multiple Suggestions. Use up and down keys to navigate"-->
             </code>
             <section aria-label="Add a filter for Company" class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 typeahead-input filter__typeahead" data-base-api-url="/jobs-guest/api/typeaheadHits?typeaheadType=COMPANY">
              <input aria-autocomplete="list" aria-controls="f_C-typeahead-list" aria-haspopup="listbox" aria-label="Add a filter" autocomplete="off" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_f_C_dismissable-input" id="f_C" maxlength="500" placeholder="Add a filter" role="combobox" type="text"/>
              <div aria-live="polite" class="typeahead-live-text sr-only" role="status">
              </div>
              <div class="typeahead-input__dropdown container-lined absolute top-[calc(100%+3px)] left-0 w-full rounded-b-md rounded-t-none z-[10] overflow-hidden max-w-none babybear:min-w-full babybear:bottom-0 babybear:overflow-y-auto">
               <template class="typeahead-item-template">
                <li class="typeahead-input__dropdown-item py-1.5 px-2 hover:cursor-pointer hover:bg-color-surface-new-hover hover:border-y-2 hover:border-solid hover:border-color-container-primary" role="option">
                 <span class="typeahead-input__dropdown-text font-sans text-sm font-bold text-color-text">
                 </span>
                </li>
               </template>
               <ul class="typeahead-input__dropdown-list w-full" id="f_C-typeahead-list" role="listbox">
               </ul>
              </div>
              <!-- -->
              <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_f_C_dismissable-input-clear" type="button">
               <label class="sr-only">
                Clear text
               </label>
               <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
               </icon>
              </button>
             </section>
             <div class="filter-values-container">
              <div aria-label="Company filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_C-0" name="f_C" type="checkbox" value="33311"/>
                <label for="f_C-0">
                 Alter Domus (1)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_C-1" name="f_C" type="checkbox" value="5908"/>
                <label for="f_C-1">
                 Cubic Corporation (1)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_C-2" name="f_C" type="checkbox" value="89938927"/>
                <label for="f_C-2">
                 CodeBeat. (1)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_C-3" name="f_C" type="checkbox" value="97222264"/>
                <label for="f_C-3">
                 toolpioneers (1)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_f_C" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Job type filter. Clicking this button displays all Job type filter options." class="filter-button pill flex items-center !min-h-0 filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_f_JT" type="button">
             Job type
             <!-- -->
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <!-- -->
             <div class="filter-values-container">
              <div aria-label="Job type filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_JT-0" name="f_JT" type="checkbox" value="F"/>
                <label for="f_JT-0">
                 Full-time (3)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_JT-1" name="f_JT" type="checkbox" value="C"/>
                <label for="f_JT-1">
                 Contract (1)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_f_JT" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Location filter. Clicking this button displays all Location filter options." class="filter-button pill flex items-center !min-h-0 filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_f_PP" type="button">
             Location
             <!-- -->
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <code id="i18n_aria_live_text_no-suggestions" style="display: none">
              <!--"No suggestions found"-->
             </code>
             <code id="i18n_aria_live_text_one-suggestion" style="display: none">
              <!--"One Suggestion. Use up and down keys to navigate"-->
             </code>
             <code id="i18n_aria_live_text_multiple-suggestions" style="display: none">
              <!--"Multiple Suggestions. Use up and down keys to navigate"-->
             </code>
             <section aria-label="Add a filter for Location" class="dismissable-input text-input !pr-3 bg-color-transparent flex items-center h-[40px] min-w-0 relative babybear:w-full babybear:mb-1 typeahead-input filter__typeahead" data-base-api-url="/jobs-guest/api/typeaheadHits?origin=jserp&amp;typeaheadType=GEO&amp;geoTypes=POPULATED_PLACE">
              <input aria-autocomplete="list" aria-controls="f_PP-typeahead-list" aria-haspopup="listbox" aria-label="Add a filter" autocomplete="off" class="dismissable-input__input font-sans text-md text-color-text bg-color-transparent flex items-center flex-1 focus:outline-none placeholder:text-color-text-secondary" data-tracking-control-name="public_jobs_f_PP_dismissable-input" id="f_PP" maxlength="500" placeholder="Add a filter" role="combobox" type="text"/>
              <div aria-live="polite" class="typeahead-live-text sr-only" role="status">
              </div>
              <div class="typeahead-input__dropdown container-lined absolute top-[calc(100%+3px)] left-0 w-full rounded-b-md rounded-t-none z-[10] overflow-hidden max-w-none babybear:min-w-full babybear:bottom-0 babybear:overflow-y-auto">
               <template class="typeahead-item-template">
                <li class="typeahead-input__dropdown-item py-1.5 px-2 hover:cursor-pointer hover:bg-color-surface-new-hover hover:border-y-2 hover:border-solid hover:border-color-container-primary" role="option">
                 <span class="typeahead-input__dropdown-text font-sans text-sm font-bold text-color-text">
                 </span>
                </li>
               </template>
               <ul class="typeahead-input__dropdown-list w-full" id="f_PP-typeahead-list" role="listbox">
               </ul>
              </div>
              <!-- -->
              <button class="dismissable-input__button text-color-text h-[40px] min-w-[24px] w-[24px] -mr-2 opacity-0 transition-opacity duration-[0.1s] disabled:invisible focus:opacity-100" data-tracking-control-name="public_jobs_f_PP_dismissable-input-clear" type="button">
               <label class="sr-only">
                Clear text
               </label>
               <icon class="dismissable-input__button-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cs55jggk4p3uqh9ozxdmpvjg7">
               </icon>
              </button>
             </section>
             <div class="filter-values-container">
              <div aria-label="Location filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_PP-0" name="f_PP" type="checkbox" value="105556991"/>
                <label for="f_PP-0">
                 Hyderabad (4)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_f_PP" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
         <li class="filter">
          <div class="dropdown-to-modal filter__dropdown-to-modal">
           <div class="collapsible-dropdown flex items-center relative hyphens-auto">
            <button aria-expanded="false" aria-label="Remote filter. Clicking this button displays all Remote filter options." class="filter-button pill flex items-center !min-h-0 filter__dropdown-to-modal-trigger" data-tracking-control-name="public_jobs_f_WT" type="button">
             Remote
             <!-- -->
             <icon class="filter-button__icon h-3 w-3" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1mbz49mpng9yvv8gqs11n7dkx">
             </icon>
            </button>
            <div class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]" tabindex="-1">
             <!-- -->
             <div class="filter-values-container">
              <div aria-label="Remote filter options" class="filter-values-container__filter-values" role="group">
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_WT-0" name="f_WT" type="checkbox" value="1"/>
                <label for="f_WT-0">
                 On-site (3)
                </label>
               </div>
               <div class="filter-values-container__filter-value">
                <input form="jserp-filters" id="f_WT-1" name="f_WT" type="checkbox" value="3"/>
                <label for="f_WT-1">
                 Hybrid (1)
                </label>
               </div>
              </div>
             </div>
             <button class="filter__submit-button" data-tracking-control-name="public_jobs_f_WT" form="jserp-filters" type="submit">
              Done
             </button>
            </div>
            <!-- -->
           </div>
          </div>
         </li>
        </ul>
       </form>
       <form action="https://www.linkedin.com/jobs/search/" class="reset-filters-button">
        <input name="keywords" type="hidden" value="Computer Science"/>
        <input name="location" type="hidden" value="Hyderabad"/>
        <input name="geoId" type="hidden" value="105556991"/>
        <button aria-label="Reset filters" class="reset-filters-button__button" data-tracking-control-name="public_jobs_filters" type="submit">
         Reset
        </button>
       </form>
      </div>
     </div>
    </div>
   </section>
   <!-- -->
   <div class="base-serp-page__content">
    <main class="two-pane-serp-page__results" id="main-content" role="main">
     <section class="two-pane-serp-page__search-header">
      <!-- -->
      <section class="job-alert-redirect-section__wrapper">
       <div class="flex justify-between items-center container-lined py-1.5 px-2 mr-2 mb-2">
        <div class="flex">
         <icon class="align-middle mr-1 w-[24px] h-[24px] flex-shrink-0" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/9wix02mt9x16d7osm90gka4zf">
         </icon>
         <span class="pr-3">
          Get notified when a new job is posted.
         </span>
        </div>
        <div>
         <button aria-labelledby="jobs-alert-switch-label" class="switch" data-modal="alert-toggle-sign-in-modal" data-tracking-control-name="public_jobs_job-alert-guest-redirect-cta" id="alert-toggle-button">
         </button>
         <label for="alert-toggle-button" id="jobs-alert-switch-label">
          Set alert
         </label>
        </div>
       </div>
       <div class="contextual-sign-in-modal" data-impression-id="public_jobs_contextual-sign-in-modal">
        <!-- -->
        <div class="">
         <!-- -->
         <div class="modal modal--contextual-sign-in" data-outlet="alert-toggle-sign-in-modal" id="alert-toggle-sign-in-modal">
          <!-- -->
          <div aria-hidden="true" class="modal__overlay flex items-center bg-color-background-scrim justify-center fixed bottom-0 left-0 right-0 top-0 opacity-0 invisible pointer-events-none z-[1000] transition-[opacity] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.17s] py-4">
           <section aria-labelledby="alert-toggle-sign-in-modal-modal-header" aria-modal="true" class="max-h-full modal__wrapper overflow-auto p-0 bg-color-surface max-w-[1128px] min-h-[160px] relative scale-[0.25] shadow-sm shadow-color-border-faint transition-[transform] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.33s] focus:outline-0 w-[1128px] mamabear:w-[744px] babybear:w-[360px] rounded-md" role="dialog" tabindex="-1">
            <button aria-label="Dismiss" class="modal__dismiss btn-tertiary h-[40px] w-[40px] p-0 rounded-full indent-0 contextual-sign-in-modal__modal-dismiss absolute right-0 m-[20px] cursor-pointer" data-tracking-control-name="public_jobs_contextual-sign-in-modal_modal_dismiss">
             <icon class="contextual-sign-in-modal__modal-dismiss-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/gs508lg3t2o81tq7pmcgn6m2">
             </icon>
            </button>
            <div class="modal__main w-full">
             <div class="contextual-sign-in-modal__screen contextual-sign-in-modal__context-screen flex flex-col my-4 mx-3">
              <img alt="" class="inline-block relative w-16 h-16 contextual-sign-in-modal__img m-auto" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/1kxd9keftj1y0os0evez0vt9u" data-ghost-classes="bg-color-entity-ghost-background" data-ghost-url="https://static.licdn.com/aero-v1/sc/h/cs8pjfgyw96g44ln9r7tct85f"/>
              <h2 class="contextual-sign-in-modal__context-screen-title font-bold font-sans text-xl text-color-text my-2 mx-4 text-center" id="alert-toggle-sign-in-modal-modal-header">
               Sign in to set job alerts for “Computer Science” roles.
              </h2>
              <!-- -->
              <!-- -->
              <div class="contextual-sign-in-modal__btn-container m-auto w-[320px] babybear:w-full">
               <!-- -->
               <div class="w-full max-w-[400px] mx-auto">
                <div class="google-auth-button">
                 <!-- -->
                 <div aria-label="Continue with google" class="google-auth-button__placeholder mx-auto" data-locale="en_US" data-logo-alignment="center" data-theme="filled_blue" role="button">
                 </div>
                 <!-- -->
                </div>
               </div>
               <div class="sign-in-modal" data-impression-id="public_jobs_contextual-sign-in-modal_sign-in-modal">
                <button class="sign-in-modal__outlet-btn cursor-pointer btn-md btn-primary btn-secondary" data-modal="alert-toggle-sign-in-modal_sign-in-modal" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_outlet-button">
                 <!-- -->
                 Sign in
                </button>
                <div class="">
                 <!-- -->
                 <div class="modal modal--sign-in" data-outlet="alert-toggle-sign-in-modal_sign-in-modal" id="alert-toggle-sign-in-modal_sign-in-modal">
                  <!-- -->
                  <div aria-hidden="true" class="modal__overlay flex items-center bg-color-background-scrim justify-center fixed bottom-0 left-0 right-0 top-0 opacity-0 invisible pointer-events-none z-[1000] transition-[opacity] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.17s] py-4">
                   <section aria-labelledby="alert-toggle-sign-in-modal_sign-in-modal-modal-header" aria-modal="true" class="max-h-full modal__wrapper overflow-auto p-0 bg-color-surface max-w-[1128px] min-h-[160px] relative scale-[0.25] shadow-sm shadow-color-border-faint transition-[transform] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.33s] focus:outline-0 w-[1128px] mamabear:w-[744px] babybear:w-[360px] rounded-md" role="dialog" tabindex="-1">
                    <button aria-label="Dismiss" class="modal__dismiss btn-tertiary h-[40px] w-[40px] p-0 rounded-full indent-0 sign-in-modal__dismiss absolute right-0 cursor-pointer m-[20px]" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_dismiss">
                     <icon class="sign-in-modal__dismiss-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/gs508lg3t2o81tq7pmcgn6m2">
                     </icon>
                    </button>
                    <div class="modal__main w-full">
                     <div class="sign-in-modal__screen flex flex-col py-4 w-[513px] babybear:w-full px-3">
                      <h2 class="sign-in-modal__header font-sans text-display-md text-color-text">
                       Welcome back
                      </h2>
                      <code id="i18n_sign_in_form_show_text" style="display: none">
                       <!--"Show"-->
                      </code>
                      <code id="i18n_sign_in_form_show_label" style="display: none">
                       <!--"Show your LinkedIn password"-->
                      </code>
                      <code id="i18n_sign_in_form_hide_text" style="display: none">
                       <!--"Hide"-->
                      </code>
                      <code id="i18n_sign_in_form_hide_label" style="display: none">
                       <!--"Hide your LinkedIn password"-->
                      </code>
                      <code id="i18n_username_error_empty" style="display: none">
                       <!--"Please enter an email address or phone number"-->
                      </code>
                      <code id="i18n_username_error_too_long" style="display: none">
                       <!--"Email or phone number must be between 3 to 128 characters"-->
                      </code>
                      <code id="i18n_username_error_too_short" style="display: none">
                       <!--"Email or phone number must be between 3 to 128 characters"-->
                      </code>
                      <code id="i18n_password_error_empty" style="display: none">
                       <!--"Please enter a password"-->
                      </code>
                      <code id="i18n_password_error_too_short" style="display: none">
                       <!--"The password you provided must have at least 6 characters"-->
                      </code>
                      <code id="i18n_password_error_too_long" style="display: none">
                       <!--"The password you provided must have at most 400 characters"-->
                      </code>
                      <!-- -->
                      <form action="https://www.linkedin.com/uas/login-submit" class="mt-1.5 mb-2" data-id="sign-in-form" method="post" novalidate="">
                       <input name="loginCsrfParam" type="hidden" value="68641b82-5f96-4c22-86ff-3e8bd79fe7ca"/>
                       <div class="flex flex-col">
                        <div class="mt-1.5" data-js-module-id="guest-input">
                         <div class="flex flex-col">
                          <label class="input-label mb-1" for="alert-toggle-sign-in-modal_sign-in-modal_session_key">
                           Email or phone
                          </label>
                          <div class="text-input flex">
                           <input autocomplete="username" class="text-color-text font-sans text-md outline-0 bg-color-transparent w-full" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-session-key" id="alert-toggle-sign-in-modal_sign-in-modal_session_key" name="session_key" required="" type="text"/>
                          </div>
                         </div>
                         <p class="input-helper mt-1.5" data-js-module-id="guest-input__message" for="alert-toggle-sign-in-modal_sign-in-modal_session_key" role="alert">
                         </p>
                        </div>
                        <div class="mt-1.5" data-js-module-id="guest-input">
                         <div class="flex flex-col">
                          <label class="input-label mb-1" for="alert-toggle-sign-in-modal_sign-in-modal_session_password">
                           Password
                          </label>
                          <div class="text-input flex">
                           <input autocomplete="current-password" class="text-color-text font-sans text-md outline-0 bg-color-transparent w-full" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-password" id="alert-toggle-sign-in-modal_sign-in-modal_session_password" name="session_password" required="" type="password"/>
                           <button aria-label="Show your LinkedIn password" aria-live="assertive" aria-relevant="text" class="font-sans text-md font-bold text-color-action z-10 ml-[12px] hover:cursor-pointer" data-id="sign-in-form__password-visibility-toggle" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-password-visibility-toggle-btn" type="button">
                            Show
                           </button>
                          </div>
                         </div>
                         <p class="input-helper mt-1.5" data-js-module-id="guest-input__message" for="alert-toggle-sign-in-modal_sign-in-modal_session_password" role="alert">
                         </p>
                        </div>
                        <input name="session_redirect" type="hidden"/>
                        <!-- -->
                       </div>
                       <div class="flex justify-between sign-in-form__footer--full-width" data-id="sign-in-form__footer">
                        <a class="font-sans text-md font-bold link leading-regular sign-in-form__forgot-password--full-width" data-id="sign-in-form__forgot-password" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_forgot_password" data-tracking-will-navigate="" href="https://www.linkedin.com/uas/request-password-reset?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_forgot_password">
                         Forgot password?
                        </a>
                        <!-- -->
                        <input name="trk" type="hidden" value="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-submit"/>
                        <button class="btn-md btn-primary flex-shrink-0 cursor-pointer sign-in-form__submit-btn--full-width" data-id="sign-in-form__submit-btn" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-submit-btn" data-tracking-litms="" type="submit">
                         Sign in
                        </button>
                       </div>
                       <div class="sign-in-form__divider left-right-divider pt-2 pb-3">
                        <p class="sign-in-form__divider-text font-sans text-sm text-color-text px-2">
                         or
                        </p>
                       </div>
                      </form>
                      <div class="w-full max-w-[400px] mx-auto">
                       <div class="google-auth-button" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_google-auth-button">
                        <p class="linkedin-tc__text text-color-text-low-emphasis text-xs pb-2" data-impression-id="public_jobs_contextual-sign-in-modal_sign-in-modal__button-skip-tc-text">
                         By clicking Continue to join or sign in, you agree to LinkedIn’s
                         <a data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_user-agreement" data-tracking-will-navigate="true" href="/legal/user-agreement?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_user-agreement" target="_blank">
                          User Agreement
                         </a>
                         ,
                         <a data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_privacy-policy" data-tracking-will-navigate="true" href="/legal/privacy-policy?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_privacy-policy" target="_blank">
                          Privacy Policy
                         </a>
                         , and
                         <a data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_cookie-policy" data-tracking-will-navigate="true" href="/legal/cookie-policy?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_cookie-policy" target="_blank">
                          Cookie Policy
                         </a>
                         .
                        </p>
                        <div aria-label="Continue with google" class="google-auth-button__placeholder mx-auto google-auth-button__placeholder--black-border" data-locale="en_US" data-logo-alignment="center" data-safe-to-skip-tnc-redirect="" data-theme="outline" role="button">
                        </div>
                        <!-- -->
                       </div>
                      </div>
                      <!-- -->
                      <p class="sign-in-modal__join-now m-auto font-sans text-md text-color-text mt-2">
                       New to LinkedIn?
                       <a class="sign-in-modal__join-link" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_join-link" data-tracking-will-navigate="true" href="https://www.linkedin.com/signup/cold-join?source=jobs_registration&amp;trk=public_jobs_contextual-sign-in-modal_sign-in-modal_join-link">
                        Join now
                       </a>
                      </p>
                     </div>
                    </div>
                    <!-- -->
                   </section>
                  </div>
                 </div>
                </div>
               </div>
               <div class="contextual-sign-in-modal__divider left-right-divider">
                <p class="contextual-sign-in-modal__divider-text font-sans text-sm text-color-text px-2">
                 or
                </p>
               </div>
              </div>
              <p class="contextual-sign-in-modal__join-now m-auto font-sans text-md text-color-text my-1">
               New to LinkedIn?
               <a class="contextual-sign-in-modal__join-link" data-tracking-control-name="public_jobs_contextual-sign-in-modal_join-link" data-tracking-will-navigate="true" href="https://www.linkedin.com/signup/cold-join?source=jobs_registration&amp;trk=public_jobs_contextual-sign-in-modal_join-link">
                Join now
               </a>
              </p>
              <p class="linkedin-tc__text text-color-text-low-emphasis text-xs pb-2 contextual-sign-in-modal__terms-and-conditions m-auto w-[320px] pt-2 babybear:w-full" data-impression-id="linkedin-tc__button-skip-tc-text">
               By clicking Continue to join or sign in, you agree to LinkedIn’s
               <a data-tracking-control-name="linkedin-tc_auth-button_user-agreement" data-tracking-will-navigate="true" href="/legal/user-agreement?trk=linkedin-tc_auth-button_user-agreement" target="_blank">
                User Agreement
               </a>
               ,
               <a data-tracking-control-name="linkedin-tc_auth-button_privacy-policy" data-tracking-will-navigate="true" href="/legal/privacy-policy?trk=linkedin-tc_auth-button_privacy-policy" target="_blank">
                Privacy Policy
               </a>
               , and
               <a data-tracking-control-name="linkedin-tc_auth-button_cookie-policy" data-tracking-will-navigate="true" href="/legal/cookie-policy?trk=linkedin-tc_auth-button_cookie-policy" target="_blank">
                Cookie Policy
               </a>
               .
              </p>
             </div>
            </div>
            <!-- -->
           </section>
          </div>
         </div>
        </div>
       </div>
      </section>
     </section>
     <!-- -->
     <div class="results-context-header">
      <h1 class="results-context-header__context">
       <span class="results-context-header__job-count">
        4
       </span>
       <span class="results-context-header__query-search">
        Computer Science Jobs in Hyderabad
       </span>
      </h1>
     </div>
     <section class="two-pane-serp-page__results-list">
      <ul class="jobs-search__results-list">
       <li>
        <div class="base-card relative w-full hover:no-underline focus:no-underline base-card--link base-search-card base-search-card--link job-search-card" data-column="1" data-entity-urn="urn:li:jobPosting:4207892829" data-impression-id="jobs-search-desktop-0" data-reference-id="uCwvCZDwt9Q3yPvFCw77ig==" data-row="1" data-tracking-id="JRKjyLJsfC7DTlBqrO1qsQ==">
         <a class="base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_search-card" data-tracking-will-navigate="" href="https://in.linkedin.com/jobs/view/system-administrator-at-cubic-corporation-4207892829?position=1&amp;pageNum=0&amp;refId=uCwvCZDwt9Q3yPvFCw77ig%3D%3D&amp;trackingId=JRKjyLJsfC7DTlBqrO1qsQ%3D%3D">
          <span class="sr-only">
           System Administrator
          </span>
         </a>
         <div class="search-entity-media">
          <img alt="" class="artdeco-entity-image artdeco-entity-image--square-4" data-delayed-url="https://media.licdn.com/dms/image/v2/D560BAQGgHdiPbvKJFw/company-logo_100_100/company-logo_100_100/0/1691605777694/cubic_logo?e=2147483647&amp;v=beta&amp;t=vpHDjz7a8B8eKQEFz7N48Ad5R4OSqPfr2XUwdAvrSao" data-ghost-classes="artdeco-entity-image--ghost" data-ghost-url="https://static.licdn.com/aero-v1/sc/h/6puxblwmhnodu6fjircz4dn4h"/>
         </div>
         <div class="base-search-card__info">
          <h3 class="base-search-card__title">
           System Administrator
          </h3>
          <h4 class="base-search-card__subtitle">
           <a class="hidden-nested-link" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_job-search-card-subtitle" data-tracking-will-navigate="" href="https://www.linkedin.com/company/cubic?trk=public_jobs_jserp-result_job-search-card-subtitle">
            Cubic Corporation
           </a>
          </h4>
          <!-- -->
          <div class="base-search-card__metadata">
           <span class="job-search-card__location">
            Hyderabad, Telangana, India
           </span>
           <div class="job-posting-benefits text-sm">
            <icon class="job-posting-benefits__icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/3p1v0uhy7uq0cm5zdvzp4eo18" data-svg-class-name="job-posting-benefits__icon-svg">
            </icon>
            <span class="job-posting-benefits__text">
             Actively Hiring
             <!-- -->
            </span>
           </div>
           <time class="job-search-card__listdate--new" datetime="2025-05-25">
            5 hours ago
           </time>
           <!-- -->
          </div>
         </div>
         <!-- -->
        </div>
       </li>
       <li>
        <div class="base-card relative w-full hover:no-underline focus:no-underline base-card--link base-search-card base-search-card--link job-search-card" data-column="1" data-entity-urn="urn:li:jobPosting:4236939624" data-impression-id="jobs-search-desktop-1" data-reference-id="uCwvCZDwt9Q3yPvFCw77ig==" data-row="2" data-tracking-id="JnYS8KKhnGFWiJ+k9F5s7Q==">
         <a class="base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_search-card" data-tracking-will-navigate="" href="https://in.linkedin.com/jobs/view/officer-1-software-engineering-development-at-alter-domus-4236939624?position=2&amp;pageNum=0&amp;refId=uCwvCZDwt9Q3yPvFCw77ig%3D%3D&amp;trackingId=JnYS8KKhnGFWiJ%2Bk9F5s7Q%3D%3D">
          <span class="sr-only">
           Officer- 1, Software Engineering- Development
          </span>
         </a>
         <div class="search-entity-media">
          <img alt="" class="artdeco-entity-image artdeco-entity-image--square-4" data-delayed-url="https://media.licdn.com/dms/image/v2/D4E0BAQEXFSzEtjoK_w/company-logo_100_100/company-logo_100_100/0/1694526813411/alter_domus_logo?e=2147483647&amp;v=beta&amp;t=eI6bjxCOunLIa1cuMyW5PM90sNyi-b9tMySShaYHh50" data-ghost-classes="artdeco-entity-image--ghost" data-ghost-url="https://static.licdn.com/aero-v1/sc/h/6puxblwmhnodu6fjircz4dn4h"/>
         </div>
         <div class="base-search-card__info">
          <h3 class="base-search-card__title">
           Officer- 1, Software Engineering- Development
          </h3>
          <h4 class="base-search-card__subtitle">
           <a class="hidden-nested-link" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_job-search-card-subtitle" data-tracking-will-navigate="" href="https://lu.linkedin.com/company/alter-domus?trk=public_jobs_jserp-result_job-search-card-subtitle">
            Alter Domus
           </a>
          </h4>
          <!-- -->
          <div class="base-search-card__metadata">
           <span class="job-search-card__location">
            Hyderabad, Telangana, India
           </span>
           <div class="job-posting-benefits text-sm">
            <icon class="job-posting-benefits__icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/3p1v0uhy7uq0cm5zdvzp4eo18" data-svg-class-name="job-posting-benefits__icon-svg">
            </icon>
            <span class="job-posting-benefits__text">
             Actively Hiring
             <!-- -->
            </span>
           </div>
           <time class="job-search-card__listdate" datetime="2025-05-24">
            1 day ago
           </time>
           <!-- -->
          </div>
         </div>
         <!-- -->
        </div>
       </li>
       <li>
        <div class="base-card relative w-full hover:no-underline focus:no-underline base-card--link base-search-card base-search-card--link job-search-card" data-column="1" data-entity-urn="urn:li:jobPosting:4234680919" data-impression-id="jobs-search-desktop-2" data-reference-id="uCwvCZDwt9Q3yPvFCw77ig==" data-row="3" data-tracking-id="4BGKAIHCvVGbpuMP+p9vDA==">
         <a class="base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_search-card" data-tracking-will-navigate="" href="https://in.linkedin.com/jobs/view/cyber-security-instructor-at-codebeat-4234680919?position=3&amp;pageNum=0&amp;refId=uCwvCZDwt9Q3yPvFCw77ig%3D%3D&amp;trackingId=4BGKAIHCvVGbpuMP%2Bp9vDA%3D%3D">
          <span class="sr-only">
           Cyber Security Instructor
          </span>
         </a>
         <div class="search-entity-media">
          <img alt="" class="artdeco-entity-image artdeco-entity-image--square-4" data-delayed-url="https://media.licdn.com/dms/image/v2/C560BAQEXHkt-wWsJvw/company-logo_100_100/company-logo_100_100/0/1672375746573?e=2147483647&amp;v=beta&amp;t=-fuGSpnOyyvrzTl9bjhfHSsGAE55ILQzW90O8e-9ma4" data-ghost-classes="artdeco-entity-image--ghost" data-ghost-url="https://static.licdn.com/aero-v1/sc/h/6puxblwmhnodu6fjircz4dn4h"/>
         </div>
         <div class="base-search-card__info">
          <h3 class="base-search-card__title">
           Cyber Security Instructor
          </h3>
          <h4 class="base-search-card__subtitle">
           <a class="hidden-nested-link" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_job-search-card-subtitle" data-tracking-will-navigate="" href="https://in.linkedin.com/company/code-beat?trk=public_jobs_jserp-result_job-search-card-subtitle">
            CodeBeat.
           </a>
          </h4>
          <!-- -->
          <div class="base-search-card__metadata">
           <span class="job-search-card__location">
            Hyderabad, Telangana, India
           </span>
           <!-- -->
           <time class="job-search-card__listdate--new" datetime="2025-05-25">
            10 hours ago
           </time>
           <!-- -->
          </div>
         </div>
         <!-- -->
        </div>
       </li>
       <li>
        <div class="base-card relative w-full hover:no-underline focus:no-underline base-card--link base-search-card base-search-card--link job-search-card" data-column="1" data-entity-urn="urn:li:jobPosting:4234690036" data-impression-id="jobs-search-desktop-3" data-reference-id="uCwvCZDwt9Q3yPvFCw77ig==" data-row="4" data-tracking-id="6SWj1Tv2hkYqmUcDSmyGJg==">
         <a class="base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_search-card" data-tracking-will-navigate="" href="https://in.linkedin.com/jobs/view/full-stack-engineer-at-toolpioneers-4234690036?position=4&amp;pageNum=0&amp;refId=uCwvCZDwt9Q3yPvFCw77ig%3D%3D&amp;trackingId=6SWj1Tv2hkYqmUcDSmyGJg%3D%3D">
          <span class="sr-only">
           Full Stack Engineer
          </span>
         </a>
         <div class="search-entity-media">
          <img alt="" class="artdeco-entity-image artdeco-entity-image--square-4" data-delayed-url="https://media.licdn.com/dms/image/v2/D560BAQHbzLLrwcn9ZQ/company-logo_100_100/company-logo_100_100/0/1699280959928/toolpioneers_logo?e=2147483647&amp;v=beta&amp;t=f8CmzAWXH0wupW9-hwH10_NFYWvePSctWhcL3SiXdoI" data-ghost-classes="artdeco-entity-image--ghost" data-ghost-url="https://static.licdn.com/aero-v1/sc/h/6puxblwmhnodu6fjircz4dn4h"/>
         </div>
         <div class="base-search-card__info">
          <h3 class="base-search-card__title">
           Full Stack Engineer
          </h3>
          <h4 class="base-search-card__subtitle">
           <a class="hidden-nested-link" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_jserp-result_job-search-card-subtitle" data-tracking-will-navigate="" href="https://in.linkedin.com/company/toolpioneers?trk=public_jobs_jserp-result_job-search-card-subtitle">
            toolpioneers
           </a>
          </h4>
          <!-- -->
          <div class="base-search-card__metadata">
           <span class="job-search-card__location">
            Hyderabad, Telangana, India
           </span>
           <!-- -->
           <time class="job-search-card__listdate--new" datetime="2025-05-25">
            8 hours ago
           </time>
           <!-- -->
          </div>
         </div>
         <!-- -->
        </div>
       </li>
      </ul>
      <!-- -->
      <div class="px-1.5 flex inline-notification text-color-signal-positive see-more-jobs__viewed-all" role="alert" type="success">
       <icon class="inline-notification__icon w-[20px] h-[20px] shrink-0 mr-[10px] inline-block" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/9zhm3eh2dq7vh2muo8xnfikxh">
       </icon>
       <p class="inline-notification__text text-sm leading-regular">
        You've viewed all jobs for this search
        <!-- -->
       </p>
      </div>
      <!-- -->
     </section>
    </main>
    <section class="two-pane-serp-page__detail-view">
     <div class="details-pane__loader details-pane__loader--hide">
      <div class="loader">
       <div class="loader__container mb-2 overflow-hidden">
        <icon class="loader__icon inline-block loader__icon--default text-color-progress-loading" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/ddi43qwelxeqjxdd45pe3fvs1" data-svg-class-name="loader__icon-svg--large fill-currentColor h-[60px] min-h-[60px] w-[60px] min-w-[60px]">
        </icon>
       </div>
      </div>
     </div>
     <div class="details-pane__content">
     </div>
    </section>
   </div>
   <!-- -->
   <footer class="li-footer bg-transparent w-full">
    <ul class="li-footer__list flex flex-wrap flex-row items-start justify-start w-full h-auto min-h-[50px] my-[0px] mx-auto py-3 px-2 papabear:w-[1128px] papabear:p-0">
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <span class="sr-only">
       LinkedIn
      </span>
      <icon class="li-footer__copy-logo text-color-logo-brand-alt inline-block self-center h-[14px] w-[56px] mr-1" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/5mebydpuuijm3uhv1q375inqh">
      </icon>
      <span class="li-footer__copy-text flex items-center">
       © 2025
      </span>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-about" data-tracking-will-navigate="" href="https://about.linkedin.com?trk=public_jobs_footer-about">
       About
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-accessibility" data-tracking-will-navigate="" href="https://www.linkedin.com/accessibility?trk=public_jobs_footer-accessibility">
       Accessibility
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-user-agreement" data-tracking-will-navigate="" href="https://www.linkedin.com/legal/user-agreement?trk=public_jobs_footer-user-agreement">
       User Agreement
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-privacy-policy" data-tracking-will-navigate="" href="https://www.linkedin.com/legal/privacy-policy?trk=public_jobs_footer-privacy-policy">
       Privacy Policy
      </a>
     </li>
     <!-- -->
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-cookie-policy" data-tracking-will-navigate="" href="https://www.linkedin.com/legal/cookie-policy?trk=public_jobs_footer-cookie-policy">
       Cookie Policy
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-copyright-policy" data-tracking-will-navigate="" href="https://www.linkedin.com/legal/copyright-policy?trk=public_jobs_footer-copyright-policy">
       Copyright Policy
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-brand-policy" data-tracking-will-navigate="" href="https://brand.linkedin.com/policies?trk=public_jobs_footer-brand-policy">
       Brand Policy
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-guest-controls" data-tracking-will-navigate="" href="https://www.linkedin.com/psettings/guest-controls?trk=public_jobs_footer-guest-controls">
       Guest Controls
      </a>
     </li>
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <a class="li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus" data-tracking-control-name="public_jobs_footer-community-guide" data-tracking-will-navigate="" href="https://www.linkedin.com/legal/professional-community-policies?trk=public_jobs_footer-community-guide">
       Community Guidelines
      </a>
     </li>
     <!-- -->
     <li class="li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto">
      <div class="collapsible-dropdown collapsible-dropdown--footer collapsible-dropdown--up flex items-center relative hyphens-auto language-selector z-2">
       <!-- -->
       <ul class="collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-[100%] top-auto" role="menu" tabindex="-1">
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="العربية (Arabic)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="ar_AE" data-tracking-control-name="language-selector-ar_AE" lang="ar_AE" role="menuitem">
          العربية (Arabic)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="বাংলা (Bangla)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="bn_IN" data-tracking-control-name="language-selector-bn_IN" lang="bn_IN" role="menuitem">
          বাংলা (Bangla)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Čeština (Czech)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="cs_CZ" data-tracking-control-name="language-selector-cs_CZ" lang="cs_CZ" role="menuitem">
          Čeština (Czech)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Dansk (Danish)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="da_DK" data-tracking-control-name="language-selector-da_DK" lang="da_DK" role="menuitem">
          Dansk (Danish)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Deutsch (German)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="de_DE" data-tracking-control-name="language-selector-de_DE" lang="de_DE" role="menuitem">
          Deutsch (German)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Ελληνικά (Greek)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="el_GR" data-tracking-control-name="language-selector-el_GR" lang="el_GR" role="menuitem">
          Ελληνικά (Greek)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="English (English) selected" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link--selected" data-locale="en_US" data-tracking-control-name="language-selector-en_US" lang="en_US" role="menuitem">
          <strong>
           English (English)
          </strong>
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Español (Spanish)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="es_ES" data-tracking-control-name="language-selector-es_ES" lang="es_ES" role="menuitem">
          Español (Spanish)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="فارسی (Persian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="fa_IR" data-tracking-control-name="language-selector-fa_IR" lang="fa_IR" role="menuitem">
          فارسی (Persian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Suomi (Finnish)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="fi_FI" data-tracking-control-name="language-selector-fi_FI" lang="fi_FI" role="menuitem">
          Suomi (Finnish)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Français (French)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="fr_FR" data-tracking-control-name="language-selector-fr_FR" lang="fr_FR" role="menuitem">
          Français (French)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="हिंदी (Hindi)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="hi_IN" data-tracking-control-name="language-selector-hi_IN" lang="hi_IN" role="menuitem">
          हिंदी (Hindi)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Magyar (Hungarian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="hu_HU" data-tracking-control-name="language-selector-hu_HU" lang="hu_HU" role="menuitem">
          Magyar (Hungarian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Bahasa Indonesia (Indonesian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="in_ID" data-tracking-control-name="language-selector-in_ID" lang="in_ID" role="menuitem">
          Bahasa Indonesia (Indonesian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Italiano (Italian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="it_IT" data-tracking-control-name="language-selector-it_IT" lang="it_IT" role="menuitem">
          Italiano (Italian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="עברית (Hebrew)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="iw_IL" data-tracking-control-name="language-selector-iw_IL" lang="iw_IL" role="menuitem">
          עברית (Hebrew)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="日本語 (Japanese)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="ja_JP" data-tracking-control-name="language-selector-ja_JP" lang="ja_JP" role="menuitem">
          日本語 (Japanese)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="한국어 (Korean)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="ko_KR" data-tracking-control-name="language-selector-ko_KR" lang="ko_KR" role="menuitem">
          한국어 (Korean)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="मराठी (Marathi)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="mr_IN" data-tracking-control-name="language-selector-mr_IN" lang="mr_IN" role="menuitem">
          मराठी (Marathi)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Bahasa Malaysia (Malay)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="ms_MY" data-tracking-control-name="language-selector-ms_MY" lang="ms_MY" role="menuitem">
          Bahasa Malaysia (Malay)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Nederlands (Dutch)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="nl_NL" data-tracking-control-name="language-selector-nl_NL" lang="nl_NL" role="menuitem">
          Nederlands (Dutch)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Norsk (Norwegian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="no_NO" data-tracking-control-name="language-selector-no_NO" lang="no_NO" role="menuitem">
          Norsk (Norwegian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="ਪੰਜਾਬੀ (Punjabi)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="pa_IN" data-tracking-control-name="language-selector-pa_IN" lang="pa_IN" role="menuitem">
          ਪੰਜਾਬੀ (Punjabi)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Polski (Polish)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="pl_PL" data-tracking-control-name="language-selector-pl_PL" lang="pl_PL" role="menuitem">
          Polski (Polish)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Português (Portuguese)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="pt_BR" data-tracking-control-name="language-selector-pt_BR" lang="pt_BR" role="menuitem">
          Português (Portuguese)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Română (Romanian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="ro_RO" data-tracking-control-name="language-selector-ro_RO" lang="ro_RO" role="menuitem">
          Română (Romanian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Русский (Russian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="ru_RU" data-tracking-control-name="language-selector-ru_RU" lang="ru_RU" role="menuitem">
          Русский (Russian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Svenska (Swedish)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="sv_SE" data-tracking-control-name="language-selector-sv_SE" lang="sv_SE" role="menuitem">
          Svenska (Swedish)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="తెలుగు (Telugu)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="te_IN" data-tracking-control-name="language-selector-te_IN" lang="te_IN" role="menuitem">
          తెలుగు (Telugu)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="ภาษาไทย (Thai)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="th_TH" data-tracking-control-name="language-selector-th_TH" lang="th_TH" role="menuitem">
          ภาษาไทย (Thai)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Tagalog (Tagalog)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="tl_PH" data-tracking-control-name="language-selector-tl_PH" lang="tl_PH" role="menuitem">
          Tagalog (Tagalog)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Türkçe (Turkish)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="tr_TR" data-tracking-control-name="language-selector-tr_TR" lang="tr_TR" role="menuitem">
          Türkçe (Turkish)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Українська (Ukrainian)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="uk_UA" data-tracking-control-name="language-selector-uk_UA" lang="uk_UA" role="menuitem">
          Українська (Ukrainian)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="Tiếng Việt (Vietnamese)" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="vi_VN" data-tracking-control-name="language-selector-vi_VN" lang="vi_VN" role="menuitem">
          Tiếng Việt (Vietnamese)
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="简体中文 (Chinese (Simplified))" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="zh_CN" data-tracking-control-name="language-selector-zh_CN" lang="zh_CN" role="menuitem">
          简体中文 (Chinese (Simplified))
         </button>
        </li>
        <li class="language-selector__item" role="presentation">
         <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->
         <button aria-label="正體中文 (Chinese (Traditional))" class="font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark language-selector__link !font-regular" data-locale="zh_TW" data-tracking-control-name="language-selector-zh_TW" lang="zh_TW" role="menuitem">
          正體中文 (Chinese (Traditional))
         </button>
        </li>
        <!-- -->
       </ul>
       <button aria-expanded="false" class="language-selector__button select-none relative pr-2 font-sans text-xs font-bold text-color-text-low-emphasis hover:text-color-link-hover hover:cursor-pointer focus:text-color-link-focus focus:outline-dotted focus:outline-1" data-tracking-control-name="footer-lang-dropdown_trigger">
        <span class="language-selector__label-text mr-0.5 break-words">
         Language
        </span>
        <icon class="language-selector__label-chevron w-2 h-2 absolute top-0 right-0" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo">
        </icon>
       </button>
      </div>
     </li>
    </ul>
    <!-- -->
   </footer>
  </div>
  <div class="guest-upsells">
   <form action="https://www.linkedin.com/uas/login-submit" class="google-auth base-google-auth" method="post">
    <input name="loginCsrfParam" type="hidden" value="68641b82-5f96-4c22-86ff-3e8bd79fe7ca"/>
    <input name="session_redirect" type="hidden" value="https://www.linkedin.com/jobs/search/?currentJobId=4234905066&amp;distance=25&amp;f_E=2&amp;f_TPR=r86400&amp;geoId=105556991&amp;keywords=computer%20science&amp;origin=JOB_SEARCH_PAGE_JOB_FILTER"/>
    <input name="trk" type="hidden" value="public_jobs_google-one-tap-submit"/>
    <div class="google-one-tap__module hidden fixed flex flex-col items-center top-[20px] right-[20px] z-[9999]">
     <div class="google-auth__tnc-container hidden relative top-2 bg-color-background-container-tint pl-2 pr-1 pt-2 pb-3 w-[375px] rounded-md shadow-2xl">
      <p class="text-md font-bold text-color-text">
       Agree &amp; Join LinkedIn
      </p>
      <p class="linkedin-tc__text text-color-text-low-emphasis text-xs pb-2 !text-sm !text-color-text" data-impression-id="public_jobs_one-tap-skip-tc-text">
       By clicking Continue to join or sign in, you agree to LinkedIn’s
       <a data-tracking-control-name="linkedin-tc_auth-button_user-agreement" data-tracking-will-navigate="true" href="/legal/user-agreement?trk=linkedin-tc_auth-button_user-agreement" target="_blank">
        User Agreement
       </a>
       ,
       <a data-tracking-control-name="linkedin-tc_auth-button_privacy-policy" data-tracking-will-navigate="true" href="/legal/privacy-policy?trk=linkedin-tc_auth-button_privacy-policy" target="_blank">
        Privacy Policy
       </a>
       , and
       <a data-tracking-control-name="linkedin-tc_auth-button_cookie-policy" data-tracking-will-navigate="true" href="/legal/cookie-policy?trk=linkedin-tc_auth-button_cookie-policy" target="_blank">
        Cookie Policy
       </a>
       .
      </p>
     </div>
     <div data-tracking-control-name="public_jobs_google-one-tap" id="google-one-tap__container">
     </div>
    </div>
    <div class="loader loader--full-screen">
     <div class="loader__container mb-2 overflow-hidden">
      <icon class="loader__icon inline-block loader__icon--default text-color-progress-loading" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/ddi43qwelxeqjxdd45pe3fvs1" data-svg-class-name="loader__icon-svg--large fill-currentColor h-[60px] min-h-[60px] w-[60px] min-w-[60px]">
      </icon>
     </div>
    </div>
   </form>
   <script data-delayed-url="https://static.licdn.com/aero-v1/sc/h/29rdkxlvag0d3cpj96fiilbju" data-module-id="google-gsi-lib">
   </script>
   <code id="isLinkedInAppWebView" style="display: none">
    <!--false-->
   </code>
   <code id="shouldRemoveUndefinedValues" style="display: none">
    <!--false-->
   </code>
   <code id="isItpSupportEnabled" style="display: none">
    <!--false-->
   </code>
   <code id="tncFlow" style="display: none">
    <!--"control"-->
   </code>
   <code id="isGoogleAuthButtonLocaleSupportEnabled" style="display: none">
    <!--true-->
   </code>
   <code id="gsiLocale" style="display: none">
    <!--"en_US"-->
   </code>
   <div class="contextual-sign-in-modal base-contextual-sign-in-modal" data-cool-off-enabled="" data-impression-id="public_jobs_contextual-sign-in-modal" data-show-on-page-load="">
    <!-- -->
    <div class="">
     <!-- -->
     <div class="modal modal--contextual-sign-in" data-outlet="base-contextual-sign-in-modal" id="base-contextual-sign-in-modal">
      <!-- -->
      <div aria-hidden="true" class="modal__overlay flex items-center bg-color-background-scrim justify-center fixed bottom-0 left-0 right-0 top-0 opacity-0 invisible pointer-events-none z-[1000] transition-[opacity] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.17s] py-4">
       <section aria-labelledby="base-contextual-sign-in-modal-modal-header" aria-modal="true" class="max-h-full modal__wrapper overflow-auto p-0 bg-color-surface max-w-[1128px] min-h-[160px] relative scale-[0.25] shadow-sm shadow-color-border-faint transition-[transform] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.33s] focus:outline-0 w-[1128px] mamabear:w-[744px] babybear:w-[360px] rounded-md" role="dialog" tabindex="-1">
        <button aria-label="Dismiss" class="modal__dismiss btn-tertiary h-[40px] w-[40px] p-0 rounded-full indent-0 contextual-sign-in-modal__modal-dismiss absolute right-0 m-[20px] cursor-pointer" data-tracking-control-name="public_jobs_contextual-sign-in-modal_modal_dismiss">
         <icon class="contextual-sign-in-modal__modal-dismiss-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/gs508lg3t2o81tq7pmcgn6m2">
         </icon>
        </button>
        <div class="modal__main w-full">
         <div class="contextual-sign-in-modal__screen contextual-sign-in-modal__context-screen flex flex-col my-4 mx-3">
          <img alt="" class="inline-block relative w-16 h-16 contextual-sign-in-modal__img m-auto" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/7e5r1tyag1womwdkqzon9r2ib" data-ghost-classes="bg-color-entity-ghost-background" data-ghost-url="https://static.licdn.com/aero-v1/sc/h/cs8pjfgyw96g44ln9r7tct85f"/>
          <h2 class="contextual-sign-in-modal__context-screen-title font-bold font-sans text-xl text-color-text my-2 mx-4 text-center" id="base-contextual-sign-in-modal-modal-header">
           Sign in to view more jobs
          </h2>
          <!-- -->
          <!-- -->
          <div class="contextual-sign-in-modal__btn-container m-auto w-[320px] babybear:w-full">
           <!-- -->
           <div class="w-full max-w-[400px] mx-auto">
            <div class="google-auth-button">
             <!-- -->
             <div aria-label="Continue with google" class="google-auth-button__placeholder mx-auto" data-locale="en_US" data-logo-alignment="center" data-theme="filled_blue" role="button">
             </div>
             <!-- -->
            </div>
           </div>
           <div class="sign-in-modal" data-impression-id="public_jobs_contextual-sign-in-modal_sign-in-modal">
            <button class="sign-in-modal__outlet-btn cursor-pointer btn-md btn-primary btn-secondary" data-modal="base-sign-in-modal" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_outlet-button">
             <!-- -->
             Sign in
            </button>
            <div class="">
             <!-- -->
             <div class="modal modal--sign-in" data-outlet="base-sign-in-modal" id="base-sign-in-modal">
              <!-- -->
              <div aria-hidden="true" class="modal__overlay flex items-center bg-color-background-scrim justify-center fixed bottom-0 left-0 right-0 top-0 opacity-0 invisible pointer-events-none z-[1000] transition-[opacity] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.17s] py-4">
               <section aria-labelledby="base-sign-in-modal-modal-header" aria-modal="true" class="max-h-full modal__wrapper overflow-auto p-0 bg-color-surface max-w-[1128px] min-h-[160px] relative scale-[0.25] shadow-sm shadow-color-border-faint transition-[transform] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.33s] focus:outline-0 w-[1128px] mamabear:w-[744px] babybear:w-[360px] rounded-md" role="dialog" tabindex="-1">
                <button aria-label="Dismiss" class="modal__dismiss btn-tertiary h-[40px] w-[40px] p-0 rounded-full indent-0 sign-in-modal__dismiss absolute right-0 cursor-pointer m-[20px]" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_dismiss">
                 <icon class="sign-in-modal__dismiss-icon" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/gs508lg3t2o81tq7pmcgn6m2">
                 </icon>
                </button>
                <div class="modal__main w-full">
                 <div class="sign-in-modal__screen flex flex-col py-4 w-[513px] babybear:w-full px-3">
                  <h2 class="sign-in-modal__header font-sans text-display-md text-color-text">
                   Welcome back
                  </h2>
                  <code id="i18n_sign_in_form_show_text" style="display: none">
                   <!--"Show"-->
                  </code>
                  <code id="i18n_sign_in_form_show_label" style="display: none">
                   <!--"Show your LinkedIn password"-->
                  </code>
                  <code id="i18n_sign_in_form_hide_text" style="display: none">
                   <!--"Hide"-->
                  </code>
                  <code id="i18n_sign_in_form_hide_label" style="display: none">
                   <!--"Hide your LinkedIn password"-->
                  </code>
                  <code id="i18n_username_error_empty" style="display: none">
                   <!--"Please enter an email address or phone number"-->
                  </code>
                  <code id="i18n_username_error_too_long" style="display: none">
                   <!--"Email or phone number must be between 3 to 128 characters"-->
                  </code>
                  <code id="i18n_username_error_too_short" style="display: none">
                   <!--"Email or phone number must be between 3 to 128 characters"-->
                  </code>
                  <code id="i18n_password_error_empty" style="display: none">
                   <!--"Please enter a password"-->
                  </code>
                  <code id="i18n_password_error_too_short" style="display: none">
                   <!--"The password you provided must have at least 6 characters"-->
                  </code>
                  <code id="i18n_password_error_too_long" style="display: none">
                   <!--"The password you provided must have at most 400 characters"-->
                  </code>
                  <!-- -->
                  <form action="https://www.linkedin.com/uas/login-submit" class="mt-1.5 mb-2" data-id="sign-in-form" method="post" novalidate="">
                   <input name="loginCsrfParam" type="hidden" value="68641b82-5f96-4c22-86ff-3e8bd79fe7ca"/>
                   <div class="flex flex-col">
                    <div class="mt-1.5" data-js-module-id="guest-input">
                     <div class="flex flex-col">
                      <label class="input-label mb-1" for="base-sign-in-modal_session_key">
                       Email or phone
                      </label>
                      <div class="text-input flex">
                       <input autocomplete="username" class="text-color-text font-sans text-md outline-0 bg-color-transparent w-full" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-session-key" id="base-sign-in-modal_session_key" name="session_key" required="" type="text"/>
                      </div>
                     </div>
                     <p class="input-helper mt-1.5" data-js-module-id="guest-input__message" for="base-sign-in-modal_session_key" role="alert">
                     </p>
                    </div>
                    <div class="mt-1.5" data-js-module-id="guest-input">
                     <div class="flex flex-col">
                      <label class="input-label mb-1" for="base-sign-in-modal_session_password">
                       Password
                      </label>
                      <div class="text-input flex">
                       <input autocomplete="current-password" class="text-color-text font-sans text-md outline-0 bg-color-transparent w-full" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-password" id="base-sign-in-modal_session_password" name="session_password" required="" type="password"/>
                       <button aria-label="Show your LinkedIn password" aria-live="assertive" aria-relevant="text" class="font-sans text-md font-bold text-color-action z-10 ml-[12px] hover:cursor-pointer" data-id="sign-in-form__password-visibility-toggle" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-password-visibility-toggle-btn" type="button">
                        Show
                       </button>
                      </div>
                     </div>
                     <p class="input-helper mt-1.5" data-js-module-id="guest-input__message" for="base-sign-in-modal_session_password" role="alert">
                     </p>
                    </div>
                    <input name="session_redirect" type="hidden" value="https://www.linkedin.com/jobs/search/?currentJobId=4234905066&amp;distance=25&amp;f_E=2&amp;f_TPR=r86400&amp;geoId=105556991&amp;keywords=computer%20science&amp;origin=JOB_SEARCH_PAGE_JOB_FILTER"/>
                    <!-- -->
                   </div>
                   <div class="flex justify-between sign-in-form__footer--full-width" data-id="sign-in-form__footer">
                    <a class="font-sans text-md font-bold link leading-regular sign-in-form__forgot-password--full-width" data-id="sign-in-form__forgot-password" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_forgot_password" data-tracking-will-navigate="" href="https://www.linkedin.com/uas/request-password-reset?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_forgot_password">
                     Forgot password?
                    </a>
                    <!-- -->
                    <input name="trk" type="hidden" value="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-submit"/>
                    <button class="btn-md btn-primary flex-shrink-0 cursor-pointer sign-in-form__submit-btn--full-width" data-id="sign-in-form__submit-btn" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_sign-in-submit-btn" data-tracking-litms="" type="submit">
                     Sign in
                    </button>
                   </div>
                   <div class="sign-in-form__divider left-right-divider pt-2 pb-3">
                    <p class="sign-in-form__divider-text font-sans text-sm text-color-text px-2">
                     or
                    </p>
                   </div>
                  </form>
                  <div class="w-full max-w-[400px] mx-auto">
                   <div class="google-auth-button" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_google-auth-button">
                    <p class="linkedin-tc__text text-color-text-low-emphasis text-xs pb-2" data-impression-id="public_jobs_contextual-sign-in-modal_sign-in-modal__button-skip-tc-text">
                     By clicking Continue to join or sign in, you agree to LinkedIn’s
                     <a data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_user-agreement" data-tracking-will-navigate="true" href="/legal/user-agreement?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_user-agreement" target="_blank">
                      User Agreement
                     </a>
                     ,
                     <a data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_privacy-policy" data-tracking-will-navigate="true" href="/legal/privacy-policy?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_privacy-policy" target="_blank">
                      Privacy Policy
                     </a>
                     , and
                     <a data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_cookie-policy" data-tracking-will-navigate="true" href="/legal/cookie-policy?trk=public_jobs_contextual-sign-in-modal_sign-in-modal_auth-button_cookie-policy" target="_blank">
                      Cookie Policy
                     </a>
                     .
                    </p>
                    <div aria-label="Continue with google" class="google-auth-button__placeholder mx-auto google-auth-button__placeholder--black-border" data-locale="en_US" data-logo-alignment="center" data-safe-to-skip-tnc-redirect="" data-theme="outline" role="button">
                    </div>
                    <!-- -->
                   </div>
                  </div>
                  <!-- -->
                  <p class="sign-in-modal__join-now m-auto font-sans text-md text-color-text mt-2">
                   New to LinkedIn?
                   <a class="sign-in-modal__join-link" data-tracking-control-name="public_jobs_contextual-sign-in-modal_sign-in-modal_join-link" data-tracking-will-navigate="true" href="https://www.linkedin.com/signup/cold-join?source=jobs_registration&amp;trk=public_jobs_contextual-sign-in-modal_sign-in-modal_join-link">
                    Join now
                   </a>
                  </p>
                 </div>
                </div>
                <!-- -->
               </section>
              </div>
             </div>
            </div>
           </div>
           <div class="contextual-sign-in-modal__divider left-right-divider">
            <p class="contextual-sign-in-modal__divider-text font-sans text-sm text-color-text px-2">
             or
            </p>
           </div>
          </div>
          <p class="contextual-sign-in-modal__join-now m-auto font-sans text-md text-color-text my-1">
           New to LinkedIn?
           <a class="contextual-sign-in-modal__join-link" data-tracking-control-name="public_jobs_contextual-sign-in-modal_join-link" data-tracking-will-navigate="true" href="https://www.linkedin.com/signup/cold-join?source=jobs_registration&amp;trk=public_jobs_contextual-sign-in-modal_join-link">
            Join now
           </a>
          </p>
          <p class="linkedin-tc__text text-color-text-low-emphasis text-xs pb-2 contextual-sign-in-modal__terms-and-conditions m-auto w-[320px] pt-2 babybear:w-full" data-impression-id="linkedin-tc__button-skip-tc-text">
           By clicking Continue to join or sign in, you agree to LinkedIn’s
           <a data-tracking-control-name="linkedin-tc_auth-button_user-agreement" data-tracking-will-navigate="true" href="/legal/user-agreement?trk=linkedin-tc_auth-button_user-agreement" target="_blank">
            User Agreement
           </a>
           ,
           <a data-tracking-control-name="linkedin-tc_auth-button_privacy-policy" data-tracking-will-navigate="true" href="/legal/privacy-policy?trk=linkedin-tc_auth-button_privacy-policy" target="_blank">
            Privacy Policy
           </a>
           , and
           <a data-tracking-control-name="linkedin-tc_auth-button_cookie-policy" data-tracking-will-navigate="true" href="/legal/cookie-policy?trk=linkedin-tc_auth-button_cookie-policy" target="_blank">
            Cookie Policy
           </a>
           .
          </p>
         </div>
        </div>
        <!-- -->
       </section>
      </div>
     </div>
    </div>
   </div>
   <div aria-describedby="cta-modal-subheader" aria-labelledby="cta-modal-header" class="cta-modal overflow-hidden container-raised z-10 fixed bottom-3 right-3 min-h-[56px] p-2 babybear:hidden windows-app-upsell windows-app-upsell--msft flex flex-col p-2 w-[359px] !bg-[#F1F8FA] opacity-90 backdrop-blur-[2px] z-1" data-impression-id="public_jobs_windows-app-upsell_cta-modal" role="dialog">
    <div class="windows-app-upsell__linkedin-title-container pt-[6px] mb-1.5 flex align-center">
     <icon class="windows-app-upsell__linkedin-bug-icon block w-[21px] h-[21px]" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/euqjj7tf5wvr33frd3x1jj9s" data-svg-class-name="windows-app-upsell__linkedin-bug-icon-svg w-[21px] h-[21px]">
     </icon>
     <p class="windows-app-upsell__linkedin-title uppercase text-xs text-color-text-secondary leading-[21px] ml-1">
      LinkedIn
     </p>
    </div>
    <p class="windows-app-upsell__title font-sans text-md text-color-text-accent-4-hover font-semibold leading-regular mb-1">
     LinkedIn is better on the app
    </p>
    <p class="windows-app-upsell__body font-sans text-sm text-color-text-secondary leading-regular">
     Don’t have the app? Get it in the Microsoft Store.
    </p>
    <a class="windows-app-upsell__cta btn-sm btn-secondary-emphasis mt-2 mb-[6px] w-fit" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_windows-app-upsell_cta" data-tracking-will-navigate="" href="ms-windows-store://pdp/?ProductId=9WZDNCRFJ4Q7&amp;mode=mini&amp;cid=guest_desktop_upsell">
     Open the app
    </a>
    <button aria-label="Dismiss" class="cta-modal__dismiss-btn absolute h-4 w-4 p-1 top-2 right-2 hover:cursor-pointer focus:outline focus:outline-2 focus:outline-color-action" data-tracking-control-name="public_jobs_windows-app-upsell_dismiss">
     <icon class="cta-modal__dismiss-icon block h-2 w-2 onload" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/adzjokfylbe8pvjr9h8iv96mw">
     </icon>
    </button>
   </div>
  </div>
  <code id="disableOneTapOnInitIfCsm" style="display: none">
   <!--false-->
  </code>
  <!-- -->
  <div aria-describedby="cta-modal-subheader" aria-labelledby="cta-modal-header" class="cta-modal overflow-hidden container-raised z-10 fixed bottom-3 right-3 min-h-[56px] p-2 babybear:hidden windows-app-upsell windows-app-upsell--msft flex flex-col p-2 w-[359px] !bg-[#F1F8FA] opacity-90 backdrop-blur-[2px] z-1 z-1" data-impression-id="public_jobs_windows-app-upsell_cta-modal" role="dialog">
   <div class="windows-app-upsell__linkedin-title-container pt-[6px] mb-1.5 flex align-center">
    <icon class="windows-app-upsell__linkedin-bug-icon block w-[21px] h-[21px]" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/euqjj7tf5wvr33frd3x1jj9s" data-svg-class-name="windows-app-upsell__linkedin-bug-icon-svg w-[21px] h-[21px]">
    </icon>
    <p class="windows-app-upsell__linkedin-title uppercase text-xs text-color-text-secondary leading-[21px] ml-1">
     LinkedIn
    </p>
   </div>
   <p class="windows-app-upsell__title font-sans text-md text-color-text-accent-4-hover font-semibold leading-regular mb-1">
    Know when new jobs open up
   </p>
   <p class="windows-app-upsell__body font-sans text-sm text-color-text-secondary leading-regular">
    Never miss a job alert with the new LinkedIn app for Windows.
   </p>
   <a class="windows-app-upsell__cta btn-sm btn-secondary-emphasis mt-2 mb-[6px] w-fit" data-tracking-client-ingraph="" data-tracking-control-name="public_jobs_windows-app-upsell_cta" data-tracking-will-navigate="" href="ms-windows-store://pdp/?ProductId=9WZDNCRFJ4Q7&amp;mode=mini&amp;cid=guest_desktop_upsell_job2">
    Get the app
   </a>
   <button aria-label="Dismiss" class="cta-modal__dismiss-btn absolute h-4 w-4 p-1 top-2 right-2 hover:cursor-pointer focus:outline focus:outline-2 focus:outline-color-action" data-tracking-control-name="public_jobs_windows-app-upsell_dismiss">
    <icon class="cta-modal__dismiss-icon block h-2 w-2 onload" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/adzjokfylbe8pvjr9h8iv96mw">
    </icon>
   </button>
  </div>
  <div class="toasts fixed z-8 babybear:right-4 mamabear:right-4 papabear:min-h-[96px] invisible top:auto bottom-4 left-4 papabear:w-[400px] toasts--bottom" id="toasts" type="bottom">
   <template id="toast-template">
    <div class="toast container-raised flex toast--bottom transition ease-accelerate duration-xxslow" data-id="toast">
     <div class="toast__message flex flex-1 babybear:items-center mamabear:items-center papabear:items-start py-2 px-1.5" data-id="toast__message" role="alert" tabindex="-1">
      <div class="toast__message-content-container" data-id="toast__message-content-container">
       <p class="toast__message-content font-sans text-sm opacity-90 inline babybear:self-center mamabear:self-center papabear:self-start" data-id="toast__message-content">
       </p>
      </div>
     </div>
     <button aria-label="Close" class="toast__dismiss-btn cursor-pointer babybear:self-center mamabear:self-center papabear:self-start pt-3 pb-2 papabear:py-2 pl-0.5 pr-0" data-id="toast__dismiss-btn">
      <svg class="fill-color-icon" height="24" width="24">
       <path d="M13 4.32 9.31 8 13 11.69 11.69 13 8 9.31 4.31 13 3 11.69 6.69 8 3 4.31 4.31 3 8 6.69 11.68 3Z">
       </path>
      </svg>
     </button>
    </div>
   </template>
   <template id="toast-icon-caution">
    <icon class="text-color-icon-caution toast__icon w-3 h-3 shrink-0 mr-2" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/bk9xca6x9l1fga1tbzame3i3l">
    </icon>
   </template>
   <template id="toast-icon-error">
    <icon class="text-color-icon-negative toast__icon w-3 h-3 shrink-0 mr-2" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/8c0098stai8lcqypn95r47dew">
    </icon>
   </template>
   <template id="toast-icon-gdpr">
    <icon class="text-color-icon-neutral toast__icon w-3 h-3 shrink-0 mr-2" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/a9phzx7id2abubo45lgookfd">
    </icon>
   </template>
   <template id="toast-icon-notify">
    <icon class="text-color-icon-neutral toast__icon w-3 h-3 shrink-0 mr-2" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/4xg53nt8deu1y7tb1t3km8tfh">
    </icon>
   </template>
   <template id="toast-icon-success">
    <icon class="text-color-icon-positive toast__icon w-3 h-3 shrink-0 mr-2" data-delayed-url="https://static.licdn.com/aero-v1/sc/h/9zhm3eh2dq7vh2muo8xnfikxh">
    </icon>
   </template>
   <template id="toast-cta">
    <a class="toast__cta cta-link font-medium ml-0.5 text-sm text-inherit inline" target="_blank">
    </a>
   </template>
  </div>
  <section id="json-refs">
   <code id="isGraphQLEnabled" style="display: none">
    <!--true-->
   </code>
   <code id="requestSubdomain" style="display: none">
    <!--"in"-->
   </code>
   <code id="pageKey" style="display: none">
    <!--"d_jobs_guest_search"-->
   </code>
   <code id="resultsPerPage" style="display: none">
    <!--25-->
   </code>
   <code id="totalResults" style="display: none">
    <!--4-->
   </code>
   <code id="i18n_redirected_from_deleted_alert" style="display: none">
    <!--"This job alert has been deactivated."-->
   </code>
   <code id="isRedirectedFromDeletedAlert" style="display: none">
    <!--false-->
   </code>
   <code id="currentSearchId" style="display: none">
    <!--"uCwvCZDwt9Q3yPvFCw77ig=="-->
   </code>
   <code id="currentJobId" style="display: none">
    <!--"4234905066"-->
   </code>
   <!-- -->
   <!-- -->
  </section>
  <script async="" src="https://static.licdn.com/aero-v1/sc/h/11w2cyeco40g892agkqchdc8a">
  </script>
  <!-- -->
  <script async="" defer="" src="https://static.licdn.com/aero-v1/sc/h/6ikx1f0pygcpwja7ehjtys5ma">
  </script>
  <!-- -->
  <!-- -->
 </body>
</html>
