{"timestamp": "2025-05-28T22:58:54.883851", "summary": {"successful": 1, "total": 5, "total_time": 117.47122502326965}, "results": [{"script": "demo_tech_news.py", "success": true, "return_code": 0, "execution_time": 62.560603857040405, "stdout": "=== AI-Powered Tech News Scraper Demo ===\nThis function scrapes tech news articles from 16 major tech news sources,\nfilters for technical content from the last 2 days, and uses AI to select\nthe most important article of the day.\n\nScraping 16 tech news websites...\nURLs:\n  - https://news.ycombinator.com/\n  - https://www.theverge.com/\n  - https://techcrunch.com/category/artificial-intelligence/\n  - https://scitechdaily.com/news/technology/\n  - https://devblogs.microsoft.com/engineering-at-microsoft/\n  - https://www.theverge.com/ai-artificial-intelligence\n  - https://arstechnica.com/ai/\n  - https://arstechnica.com/gadgets/\n  - https://www.analyticsinsight.net/news\n  - https://www.innovationnewsnetwork.com/artificial-intelligence/\n  - https://www.innovationnewsnetwork.com/computer-science/\n  - https://www.innovationnewsnetwork.com/cybersecurity/\n  - https://www.innovationnewsnetwork.com/category/quantum-news/\n  - https://techxplore.com/hi-tech-news/\n  - https://openai.com/news/\n  - https://www.linkedin.com/blog/engineering\n\nFiltering criteria:\n  - Articles published within the last 2 days (today and yesterday)\n  - Titles containing technical keywords (AI, ML, blockchain, etc.)\n  - Excluding business drama and non-technical content\n  - AI selection for most important article covering:\n    • New AI models/tools/breakthroughs\n    • Tech product innovation\n    • Developer tools/cloud/hardware updates\n\n=== RESULTS ===\nFound 2 relevant tech articles from the last 2 days:\n\nResults saved to todays_tech_news.json\nError occurred: 'charmap' codec can't encode character '\\U0001f916' in position 2: character maps to <undefined>\n", "stderr": "2025-05-28 22:56:48,618 - INFO - Scraping: https://news.ycombinator.com/\n2025-05-28 22:56:52,506 - INFO - Completed scraping https://news.ycombinator.com/ - found 0 relevant articles\n2025-05-28 22:56:52,506 - INFO - Scraping: https://www.theverge.com/\n2025-05-28 22:56:56,525 - INFO - Completed scraping https://www.theverge.com/ - found 0 relevant articles\n2025-05-28 22:56:56,525 - INFO - Scraping: https://techcrunch.com/category/artificial-intelligence/\n2025-05-28 22:57:00,080 - INFO - Completed scraping https://techcrunch.com/category/artificial-intelligence/ - found 0 relevant articles\n2025-05-28 22:57:00,080 - INFO - Scraping: https://scitechdaily.com/news/technology/\n2025-05-28 22:57:02,305 - WARNING - HTTP error 403 while fetching https://scitechdaily.com/news/technology/\n2025-05-28 22:57:02,306 - INFO - Scraping: https://devblogs.microsoft.com/engineering-at-microsoft/\n2025-05-28 22:57:05,892 - INFO - Completed scraping https://devblogs.microsoft.com/engineering-at-microsoft/ - found 0 relevant articles\n2025-05-28 22:57:05,893 - INFO - Scraping: https://www.theverge.com/ai-artificial-intelligence\n2025-05-28 22:57:09,896 - INFO - Completed scraping https://www.theverge.com/ai-artificial-intelligence - found 0 relevant articles\n2025-05-28 22:57:09,896 - INFO - Scraping: https://arstechnica.com/ai/\n2025-05-28 22:57:14,225 - INFO - Completed scraping https://arstechnica.com/ai/ - found 0 relevant articles\n2025-05-28 22:57:14,225 - INFO - Scraping: https://arstechnica.com/gadgets/\n2025-05-28 22:57:18,928 - INFO - Completed scraping https://arstechnica.com/gadgets/ - found 0 relevant articles\n2025-05-28 22:57:18,928 - INFO - Scraping: https://www.analyticsinsight.net/news\n2025-05-28 22:57:22,897 - INFO - Completed scraping https://www.analyticsinsight.net/news - found 0 relevant articles\n2025-05-28 22:57:22,897 - INFO - Scraping: https://www.innovationnewsnetwork.com/artificial-intelligence/\n2025-05-28 22:57:26,979 - INFO - Completed scraping https://www.innovationnewsnetwork.com/artificial-intelligence/ - found 0 relevant articles\n2025-05-28 22:57:26,979 - INFO - Scraping: https://www.innovationnewsnetwork.com/computer-science/\n2025-05-28 22:57:30,964 - INFO - Completed scraping https://www.innovationnewsnetwork.com/computer-science/ - found 0 relevant articles\n2025-05-28 22:57:30,964 - INFO - Scraping: https://www.innovationnewsnetwork.com/cybersecurity/\n2025-05-28 22:57:36,022 - INFO - Completed scraping https://www.innovationnewsnetwork.com/cybersecurity/ - found 0 relevant articles\n2025-05-28 22:57:36,022 - INFO - Scraping: https://www.innovationnewsnetwork.com/category/quantum-news/\n2025-05-28 22:57:39,408 - INFO - Completed scraping https://www.innovationnewsnetwork.com/category/quantum-news/ - found 0 relevant articles\n2025-05-28 22:57:39,408 - INFO - Scraping: https://techxplore.com/hi-tech-news/\n2025-05-28 22:57:42,638 - INFO - Found relevant technical article: Algorithm improves acoustic sensor accuracy for ch...\n2025-05-28 22:57:42,663 - INFO - Found relevant technical article: GPT-4 matches human performance on analogical reas...\n2025-05-28 22:57:42,688 - INFO - Completed scraping https://techxplore.com/hi-tech-news/ - found 0 relevant articles\n2025-05-28 22:57:42,688 - INFO - Scraping: https://openai.com/news/\n2025-05-28 22:57:45,099 - WARNING - HTTP error 403 while fetching https://openai.com/news/\n2025-05-28 22:57:45,099 - INFO - Scraping: https://www.linkedin.com/blog/engineering\n2025-05-28 22:57:49,787 - INFO - Completed scraping https://www.linkedin.com/blog/engineering - found 0 relevant articles\n2025-05-28 22:57:49,787 - INFO - No history file found, starting fresh\n2025-05-28 22:57:49,787 - INFO - No history file found, starting fresh\n2025-05-28 22:57:49,787 - INFO - Filtered out 0 duplicate articles, 2 remaining\n2025-05-28 22:57:49,787 - INFO - === TECH NEWS FILTERING STATISTICS ===\n2025-05-28 22:57:49,787 - INFO - Total articles scraped: 326\n2025-05-28 22:57:49,787 - INFO - Filtered by date (48-hour): 207\n2025-05-28 22:57:49,787 - INFO - Filtered by keywords: 117\n2025-05-28 22:57:49,787 - INFO - Filtered as duplicates: 0\n2025-05-28 22:57:49,788 - INFO - Final articles count: 2\n2025-05-28 22:57:49,788 - INFO - Overall filter rate: 99.4%\n2025-05-28 22:57:49,788 - INFO - =====================================\n", "timeout": false}, {"script": "internship_scraper.py", "success": false, "return_code": 1, "execution_time": 17.239269256591797, "stdout": "\n============================================================\n", "stderr": "2025-05-28 22:57:53,247 - INFO - === Starting Intelligent Internship Scraper ===\n2025-05-28 22:57:53,247 - INFO - Stage 1: Scraping internships from all sources...\n2025-05-28 22:57:53,247 - INFO - Scraping Internshala with STRICT 48-hour posting date filtering...\n2025-05-28 22:57:53,251 - DEBUG - Starting new HTTPS connection (1): internshala.com:443\n2025-05-28 22:57:54,912 - DEBUG - https://internshala.com:443 \"GET /internships/computer-science-internship-in-hyderabad/ HTTP/1.1\" 200 None\n2025-05-28 22:57:55,258 - INFO - Successfully fetched Internshala page\n2025-05-28 22:57:55,260 - INFO - Found 23 h3 headings to analyze\n2025-05-28 22:57:55,263 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Software Testing': '6 Months'\n2025-05-28 22:57:55,263 - DEBUG - Found posting date text: '1 day ago'\n2025-05-28 22:57:55,263 - DEBUG - \\u2705 POSTING DATE found for 'Software Testing': '1 day ago'\n2025-05-28 22:57:55,263 - DEBUG - \\u2705 KEEPING 'Software Testing' - posted 1 day ago (within 48 hours)\n2025-05-28 22:57:55,263 - DEBUG - Added recent internship: Software Testing at Freshers Job Fair .IN (posted 1 day ago)\n2025-05-28 22:57:55,266 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Software Development': '6 Months'\n2025-05-28 22:57:55,266 - DEBUG - Found posting date text: '2 days ago'\n2025-05-28 22:57:55,266 - DEBUG - \\u2705 POSTING DATE found for 'Software Development': '2 days ago'\n2025-05-28 22:57:55,266 - DEBUG - \\u2705 KEEPING 'Software Development' - posted 2 days ago (within 48 hours)\n2025-05-28 22:57:55,266 - DEBUG - Added recent internship: Software Development at Freshers Job Fair .IN (posted 2 days ago)\n2025-05-28 22:57:55,269 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'PHP Development': '3 Months'\n2025-05-28 22:57:55,270 - DEBUG - Found posting date text: '6 days ago'\n2025-05-28 22:57:55,270 - DEBUG - \\u2705 POSTING DATE found for 'PHP Development': '6 days ago'\n2025-05-28 22:57:55,270 - DEBUG - \\u274c FILTERED OUT 'PHP Development' - posted 6 days ago (outside 48-hour window)\n2025-05-28 22:57:55,273 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'MERN Developer': '6 Months'\n2025-05-28 22:57:55,273 - DEBUG - Found posting date text: '6 days ago'\n2025-05-28 22:57:55,273 - DEBUG - \\u2705 POSTING DATE found for 'MERN Developer': '6 days ago'\n2025-05-28 22:57:55,273 - DEBUG - \\u274c FILTERED OUT 'MERN Developer' - posted 6 days ago (outside 48-hour window)\n2025-05-28 22:57:55,275 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Moodle Developer': '6 Months'\n2025-05-28 22:57:55,276 - DEBUG - Found posting date text: '7 days ago'\n2025-05-28 22:57:55,276 - DEBUG - \\u2705 POSTING DATE found for 'Moodle Developer': '7 days ago'\n2025-05-28 22:57:55,276 - DEBUG - \\u274c FILTERED OUT 'Moodle Developer' - posted 7 days ago (outside 48-hour window)\n2025-05-28 22:57:55,278 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Computer Operator': '2 Months'\n2025-05-28 22:57:55,278 - DEBUG - Found posting date text: '7 days ago'\n2025-05-28 22:57:55,278 - DEBUG - \\u2705 POSTING DATE found for 'Computer Operator': '7 days ago'\n2025-05-28 22:57:55,278 - DEBUG - \\u274c FILTERED OUT 'Computer Operator' - posted 7 days ago (outside 48-hour window)\n2025-05-28 22:57:55,281 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Python Development': '3 Months'\n2025-05-28 22:57:55,281 - DEBUG - Found posting date text: '7 days ago'\n2025-05-28 22:57:55,281 - DEBUG - \\u2705 POSTING DATE found for 'Python Development': '7 days ago'\n2025-05-28 22:57:55,281 - DEBUG - \\u274c FILTERED OUT 'Python Development' - posted 7 days ago (outside 48-hour window)\n2025-05-28 22:57:55,284 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Robotics Engineer': '6 Months'\n2025-05-28 22:57:55,284 - DEBUG - Found posting date text: '1 week ago'\n2025-05-28 22:57:55,284 - DEBUG - \\u2705 POSTING DATE found for 'Robotics Engineer': '1 week ago'\n2025-05-28 22:57:55,284 - DEBUG - \\u274c FILTERED OUT 'Robotics Engineer' - posted 1 week ago (outside 48-hour window)\n2025-05-28 22:57:55,287 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Computer Hardware & Networking Technician': '3 Months'\n2025-05-28 22:57:55,287 - DEBUG - Found posting date text: '1 week ago'\n2025-05-28 22:57:55,287 - DEBUG - \\u2705 POSTING DATE found for 'Computer Hardware & Networking Technician': '1 week ago'\n2025-05-28 22:57:55,287 - DEBUG - \\u274c FILTERED OUT 'Computer Hardware & Networking Technician' - posted 1 week ago (outside 48-hour window)\n2025-05-28 22:57:55,289 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Finance Automation & Web Operations': '3 Months'\n2025-05-28 22:57:55,290 - DEBUG - Found posting date text: '1 week ago'\n2025-05-28 22:57:55,290 - DEBUG - \\u2705 POSTING DATE found for 'Finance Automation & Web Operations': '1 week ago'\n2025-05-28 22:57:55,290 - DEBUG - \\u274c FILTERED OUT 'Finance Automation & Web Operations' - posted 1 week ago (outside 48-hour window)\n2025-05-28 22:57:55,292 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'PHP Development': '3 Months'\n2025-05-28 22:57:55,292 - DEBUG - Found posting date text: '1 week ago'\n2025-05-28 22:57:55,293 - DEBUG - \\u2705 POSTING DATE found for 'PHP Development': '1 week ago'\n2025-05-28 22:57:55,293 - DEBUG - \\u274c FILTERED OUT 'PHP Development' - posted 1 week ago (outside 48-hour window)\n2025-05-28 22:57:55,295 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Front End Development': '3 Months'\n2025-05-28 22:57:55,295 - DEBUG - Found posting date text: '1 week ago'\n2025-05-28 22:57:55,295 - DEBUG - \\u2705 POSTING DATE found for 'Front End Development': '1 week ago'\n2025-05-28 22:57:55,295 - DEBUG - \\u274c FILTERED OUT 'Front End Development' - posted 1 week ago (outside 48-hour window)\n2025-05-28 22:57:55,298 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Front End Development': '6 Months'\n2025-05-28 22:57:55,298 - DEBUG - Found posting date text: '2 weeks ago'\n2025-05-28 22:57:55,298 - DEBUG - \\u2705 POSTING DATE found for 'Front End Development': '2 weeks ago'\n2025-05-28 22:57:55,298 - DEBUG - \\u274c FILTERED OUT 'Front End Development' - posted 2 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,300 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Product Manager': '3 Months'\n2025-05-28 22:57:55,301 - DEBUG - Found posting date text: '2 weeks ago'\n2025-05-28 22:57:55,301 - DEBUG - \\u2705 POSTING DATE found for 'Product Manager': '2 weeks ago'\n2025-05-28 22:57:55,301 - DEBUG - \\u274c FILTERED OUT 'Product Manager' - posted 2 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,303 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Backend Development': '3 Months'\n2025-05-28 22:57:55,303 - DEBUG - Found posting date text: '2 weeks ago'\n2025-05-28 22:57:55,304 - DEBUG - \\u2705 POSTING DATE found for 'Backend Development': '2 weeks ago'\n2025-05-28 22:57:55,304 - DEBUG - \\u274c FILTERED OUT 'Backend Development' - posted 2 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,306 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Flutter Development': '3 Months'\n2025-05-28 22:57:55,306 - DEBUG - Found posting date text: '2 weeks ago'\n2025-05-28 22:57:55,306 - DEBUG - \\u2705 POSTING DATE found for 'Flutter Development': '2 weeks ago'\n2025-05-28 22:57:55,306 - DEBUG - \\u274c FILTERED OUT 'Flutter Development' - posted 2 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,309 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Product Management': '3 Months'\n2025-05-28 22:57:55,309 - DEBUG - Found posting date text: '2 weeks ago'\n2025-05-28 22:57:55,309 - DEBUG - \\u2705 POSTING DATE found for 'Product Management': '2 weeks ago'\n2025-05-28 22:57:55,309 - DEBUG - \\u274c FILTERED OUT 'Product Management' - posted 2 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,312 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'WordPress Development': '4 Months'\n2025-05-28 22:57:55,312 - DEBUG - Found posting date text: '3 weeks ago'\n2025-05-28 22:57:55,312 - DEBUG - \\u2705 POSTING DATE found for 'WordPress Development': '3 weeks ago'\n2025-05-28 22:57:55,312 - DEBUG - \\u274c FILTERED OUT 'WordPress Development' - posted 3 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,314 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'React Native Development': '3 Months'\n2025-05-28 22:57:55,314 - DEBUG - Found posting date text: '3 weeks ago'\n2025-05-28 22:57:55,315 - DEBUG - \\u2705 POSTING DATE found for 'React Native Development': '3 weeks ago'\n2025-05-28 22:57:55,315 - DEBUG - \\u274c FILTERED OUT 'React Native Development' - posted 3 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,317 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'Full Stack Development': '3 Months'\n2025-05-28 22:57:55,317 - DEBUG - Found posting date text: '3 weeks ago'\n2025-05-28 22:57:55,317 - DEBUG - \\u2705 POSTING DATE found for 'Full Stack Development': '3 weeks ago'\n2025-05-28 22:57:55,317 - DEBUG - \\u274c FILTERED OUT 'Full Stack Development' - posted 3 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,320 - DEBUG - \\u23f1\\ufe0f DURATION (ignored) for 'UX/UI Design': '6 Months'\n2025-05-28 22:57:55,320 - DEBUG - Found posting date text: '3 weeks ago'\n2025-05-28 22:57:55,320 - DEBUG - \\u2705 POSTING DATE found for 'UX/UI Design': '3 weeks ago'\n2025-05-28 22:57:55,320 - DEBUG - \\u274c FILTERED OUT 'UX/UI Design' - posted 3 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,323 - DEBUG - Found posting date text: '3 weeks ago'\n2025-05-28 22:57:55,323 - DEBUG - \\u2705 POSTING DATE found for 'Interim Engineering Intern – Software': '3 weeks ago'\n2025-05-28 22:57:55,323 - DEBUG - \\u274c FILTERED OUT 'Interim Engineering Intern – Software' - posted 3 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,325 - DEBUG - Found posting date text: '3 weeks ago'\n2025-05-28 22:57:55,326 - DEBUG - \\u2705 POSTING DATE found for 'Interim Engineering Intern': '3 weeks ago'\n2025-05-28 22:57:55,326 - DEBUG - \\u274c FILTERED OUT 'Interim Engineering Intern' - posted 3 weeks ago (outside 48-hour window)\n2025-05-28 22:57:55,326 - INFO - Internshala 48-hour STRICT filter results:\n2025-05-28 22:57:55,326 - INFO -   Total internships found: 23\n2025-05-28 22:57:55,326 - INFO -   After 48-hour filter: 2\n2025-05-28 22:57:55,326 - INFO -   Filtered out: 21 internships (older than 48 hours or no posting date)\n2025-05-28 22:57:55,326 - INFO - Sample 1: Software Testing at Freshers Job Fair .IN\n2025-05-28 22:57:55,326 - INFO - Sample 2: Software Development at Freshers Job Fair .IN\n2025-05-28 22:57:55,326 - INFO - SUCCESS Internshala: 2 internships\n2025-05-28 22:57:55,327 - INFO - Scraping LinkedIn with LENIENT filtering (URL pre-filtered for 24h)...\n2025-05-28 22:57:55,328 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:57:56,781 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?currentJobId=4234163468&distance=25&f_E=1&f_TPR=r86400&geoId=105556991&keywords=computer%20science&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 16346\n2025-05-28 22:57:56,955 - INFO - Successfully fetched LinkedIn page\n2025-05-28 22:57:56,959 - DEBUG - Found 4 cards with selector: 'div[data-entity-urn*=\"job\"]'\n2025-05-28 22:57:56,963 - DEBUG - Found 4 cards with selector: 'div.base-card'\n2025-05-28 22:57:56,972 - DEBUG - Found 4 cards with selector: 'div.job-search-card'\n2025-05-28 22:57:56,977 - DEBUG - Found 4 cards with selector: 'div.base-search-card'\n2025-05-28 22:57:57,013 - DEBUG - Found 9 cards with selector: 'div[class*=\"job\"]'\n2025-05-28 22:57:57,017 - INFO - Found 7 total unique job cards to analyze\n2025-05-28 22:57:57,017 - DEBUG - Sample card 1 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card'], data-attrs=['data-entity-urn', 'data-impression-id', 'data-reference-id', 'data-tracking-id', 'data-column', 'data-row']\n2025-05-28 22:57:57,017 - DEBUG - Sample card 2 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card'], data-attrs=['data-entity-urn', 'data-impression-id', 'data-reference-id', 'data-tracking-id', 'data-column', 'data-row']\n2025-05-28 22:57:57,017 - DEBUG - Sample card 3 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card'], data-attrs=['data-entity-urn', 'data-impression-id', 'data-reference-id', 'data-tracking-id', 'data-column', 'data-row']\n2025-05-28 22:57:57,017 - DEBUG - Processing card 1/7\n2025-05-28 22:57:57,018 - DEBUG - Card 1 contains 'promoted': False\n2025-05-28 22:57:57,019 - DEBUG - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:57:57,019 - DEBUG - \\u2705 Card 1 title extracted: 'Network Engineer Intern'\n2025-05-28 22:57:57,019 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:57:57,019 - DEBUG - \\U0001f4c5 POSTING DATE found for LinkedIn 'Network Engineer Intern': '10 hours ago' (info only - not filtering)\n2025-05-28 22:57:57,019 - DEBUG - \\u2705 KEEPING LinkedIn 'Network Engineer Intern' - posted 10 hours ago (date filtering skipped)\n2025-05-28 22:57:57,020 - DEBUG - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:57:57,020 - DEBUG - \\u2705 Card 1 company extracted: 'Ninternet'\n2025-05-28 22:57:57,020 - DEBUG - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:57:57,020 - DEBUG - \\u2705 Card 1 URL extracted: 'https://in.linkedin.com/jobs/view/network-engineer-intern-at-ninternet-4236082567?position=1&pageNum=0&refId=dr4HAim88jzE4XcalD%2BERQ%3D%3D&trackingId=7tWDmSzGmG4NmawHfIPUrA%3D%3D'\n2025-05-28 22:57:57,020 - DEBUG - \\U0001f389 Card 1 SUCCESSFULLY ADDED: 'Network Engineer Intern' at 'Ninternet'\n2025-05-28 22:57:57,020 - DEBUG - Added LinkedIn internship: Network Engineer Intern at Ninternet (posted 10 hours ago)\n2025-05-28 22:57:57,020 - DEBUG - Processing card 2/7\n2025-05-28 22:57:57,021 - DEBUG - Card 2 contains 'promoted': False\n2025-05-28 22:57:57,021 - DEBUG - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:57:57,021 - DEBUG - \\u2705 Card 2 title extracted: 'Trainee Consultant - SAP ABAP Job'\n2025-05-28 22:57:57,022 - DEBUG - Found posting date text: '5 hours ago'\n2025-05-28 22:57:57,022 - DEBUG - \\U0001f4c5 POSTING DATE found for LinkedIn 'Trainee Consultant - SAP ABAP Job': '5 hours ago' (info only - not filtering)\n2025-05-28 22:57:57,022 - DEBUG - \\u2705 KEEPING LinkedIn 'Trainee Consultant - SAP ABAP Job' - posted 5 hours ago (date filtering skipped)\n2025-05-28 22:57:57,022 - DEBUG - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:57:57,022 - DEBUG - \\u2705 Card 2 company extracted: 'YASH Technologies'\n2025-05-28 22:57:57,022 - DEBUG - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:57:57,022 - DEBUG - \\u2705 Card 2 URL extracted: 'https://in.linkedin.com/jobs/view/trainee-consultant-sap-abap-job-at-yash-technologies-4236581593?position=2&pageNum=0&refId=dr4HAim88jzE4XcalD%2BERQ%3D%3D&trackingId=w1vJrRuU86LAiU2qG8gbVA%3D%3D'\n2025-05-28 22:57:57,022 - DEBUG - \\U0001f389 Card 2 SUCCESSFULLY ADDED: 'Trainee Consultant - SAP ABAP Job' at 'YASH Technologies'\n2025-05-28 22:57:57,023 - DEBUG - Added LinkedIn internship: Trainee Consultant - SAP ABAP Job at YASH Technologies (posted 5 hours ago)\n2025-05-28 22:57:57,023 - DEBUG - Processing card 3/7\n2025-05-28 22:57:57,023 - DEBUG - Card 3 contains 'promoted': False\n2025-05-28 22:57:57,023 - DEBUG - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:57:57,024 - DEBUG - \\u2705 Card 3 title extracted: 'Trainee Consultant - SAP ABAP Job'\n2025-05-28 22:57:57,024 - DEBUG - Found posting date text: '4 hours ago'\n2025-05-28 22:57:57,024 - DEBUG - \\U0001f4c5 POSTING DATE found for LinkedIn 'Trainee Consultant - SAP ABAP Job': '4 hours ago' (info only - not filtering)\n2025-05-28 22:57:57,024 - DEBUG - \\u2705 KEEPING LinkedIn 'Trainee Consultant - SAP ABAP Job' - posted 4 hours ago (date filtering skipped)\n2025-05-28 22:57:57,024 - DEBUG - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:57:57,024 - DEBUG - \\u2705 Card 3 company extracted: 'YASH Technologies Middle East'\n2025-05-28 22:57:57,024 - DEBUG - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:57:57,024 - DEBUG - \\u2705 Card 3 URL extracted: 'https://in.linkedin.com/jobs/view/trainee-consultant-sap-abap-job-at-yash-technologies-middle-east-4236341620?position=3&pageNum=0&refId=dr4HAim88jzE4XcalD%2BERQ%3D%3D&trackingId=a%2FDjzkjpSsFhbktPKVNvng%3D%3D'\n2025-05-28 22:57:57,025 - DEBUG - \\U0001f389 Card 3 SUCCESSFULLY ADDED: 'Trainee Consultant - SAP ABAP Job' at 'YASH Technologies Middle East'\n2025-05-28 22:57:57,025 - DEBUG - Added LinkedIn internship: Trainee Consultant - SAP ABAP Job at YASH Technologies Middle East (posted 4 hours ago)\n2025-05-28 22:57:57,025 - DEBUG - Processing card 4/7\n2025-05-28 22:57:57,025 - DEBUG - Card 4 contains 'promoted': False\n2025-05-28 22:57:57,026 - DEBUG - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:57:57,026 - DEBUG - \\u2705 Card 4 title extracted: 'Mern stack Intern (2024/2025 Passedouts only)'\n2025-05-28 22:57:57,026 - DEBUG - Found posting date text: '1 hour ago'\n2025-05-28 22:57:57,026 - DEBUG - \\U0001f4c5 POSTING DATE found for LinkedIn 'Mern stack Intern (2024/2025 Passedouts only)': '1 hour ago' (info only - not filtering)\n2025-05-28 22:57:57,026 - DEBUG - \\u2705 KEEPING LinkedIn 'Mern stack Intern (2024/2025 Passedouts only)' - posted 1 hour ago (date filtering skipped)\n2025-05-28 22:57:57,026 - DEBUG - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:57:57,026 - DEBUG - \\u2705 Card 4 company extracted: 'techolution'\n2025-05-28 22:57:57,027 - DEBUG - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:57:57,027 - DEBUG - \\u2705 Card 4 URL extracted: 'https://in.linkedin.com/jobs/view/mern-stack-intern-2024-2025-passedouts-only-at-techolution-4236819501?position=4&pageNum=0&refId=dr4HAim88jzE4XcalD%2BERQ%3D%3D&trackingId=rrUseVu82Q8q633rSGpgPw%3D%3D'\n2025-05-28 22:57:57,027 - DEBUG - \\U0001f389 Card 4 SUCCESSFULLY ADDED: 'Mern stack Intern (2024/2025 Passedouts only)' at 'techolution'\n2025-05-28 22:57:57,027 - DEBUG - Added LinkedIn internship: Mern stack Intern (2024/2025 Passedouts only) at techolution (posted 1 hour ago)\n2025-05-28 22:57:57,027 - DEBUG - Processing card 5/7\n2025-05-28 22:57:57,027 - DEBUG - Card 5 contains 'promoted': False\n2025-05-28 22:57:57,028 - DEBUG - \\u274c No title found for card 5 with classes: ['job-alert-redirect-section__container'] - SKIPPING\n2025-05-28 22:57:57,029 - DEBUG - Card 5 HTML snippet: <div class=\"job-alert-redirect-section__container\">\n<p>\n            Get notified about new <span class=\"break-words t-bold\">Computer Science</span> jobs in <strong>Hyderabad</strong>.\n          </p>\n<...\n2025-05-28 22:57:57,029 - DEBUG - Processing card 6/7\n2025-05-28 22:57:57,029 - DEBUG - Card 6 contains 'promoted': False\n2025-05-28 22:57:57,029 - DEBUG - \\u274c No title found for card 6 with classes: ['job-posting-benefits', 'text-sm'] - SKIPPING\n2025-05-28 22:57:57,030 - DEBUG - Card 6 HTML snippet: <div class=\"job-posting-benefits text-sm\">\n<icon class=\"job-posting-benefits__icon\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/3p1v0uhy7uq0cm5zdvzp4eo18\" data-svg-class-name=\"job-posting-...\n2025-05-28 22:57:57,030 - DEBUG - Processing card 7/7\n2025-05-28 22:57:57,030 - DEBUG - Card 7 contains 'promoted': False\n2025-05-28 22:57:57,030 - DEBUG - \\u274c No title found for card 7 with classes: ['px-1.5', 'flex', 'inline-notification', 'text-color-signal-positive', 'see-more-jobs__viewed-all'] - SKIPPING\n2025-05-28 22:57:57,030 - DEBUG - Card 7 HTML snippet: <div class=\"px-1.5 flex inline-notification text-color-signal-positive see-more-jobs__viewed-all\" role=\"alert\" type=\"success\">\n<icon class=\"inline-notification__icon w-[20px] h-[20px] shrink-0 mr-[10p...\n2025-05-28 22:57:57,031 - INFO - LinkedIn LENIENT filter results (URL pre-filtered for 24h):\n2025-05-28 22:57:57,031 - INFO -   Total internships found: 4\n2025-05-28 22:57:57,031 - INFO -   After lenient processing: 4\n2025-05-28 22:57:57,031 - INFO -   Date filtering: SKIPPED (LinkedIn URL already filters for 24 hours)\n2025-05-28 22:57:57,031 - INFO - Sample 1: Network Engineer Intern at Ninternet\n2025-05-28 22:57:57,031 - INFO - Sample 2: Trainee Consultant - SAP ABAP Job at YASH Technologies\n2025-05-28 22:57:57,031 - INFO - Sample 3: Trainee Consultant - SAP ABAP Job at YASH Technologies Middle East\n2025-05-28 22:57:57,031 - INFO - SUCCESS LinkedIn: 4 internships\n2025-05-28 22:57:57,031 - INFO - Scraping LetsIntern...\n2025-05-28 22:57:57,032 - DEBUG - Starting new HTTPS connection (1): letsintern.com:443\n2025-05-28 22:57:58,991 - DEBUG - https://letsintern.com:443 \"GET /internships/in/hyderabad HTTP/1.1\" 520 0\n2025-05-28 22:57:58,992 - WARNING - HTTP error 520 for https://letsintern.com/internships/in/hyderabad (attempt 1/3)\n2025-05-28 22:58:00,174 - DEBUG - Starting new HTTPS connection (1): letsintern.com:443\n2025-05-28 22:58:02,566 - DEBUG - https://letsintern.com:443 \"GET /internships/in/hyderabad HTTP/1.1\" 520 0\n2025-05-28 22:58:02,567 - WARNING - HTTP error 520 for https://letsintern.com/internships/in/hyderabad (attempt 2/3)\n2025-05-28 22:58:04,220 - DEBUG - Starting new HTTPS connection (1): letsintern.com:443\n2025-05-28 22:58:06,627 - DEBUG - https://letsintern.com:443 \"GET /internships/in/hyderabad HTTP/1.1\" 520 0\n2025-05-28 22:58:06,629 - WARNING - HTTP error 520 for https://letsintern.com/internships/in/hyderabad (attempt 3/3)\n2025-05-28 22:58:06,629 - ERROR - Failed to fetch https://letsintern.com/internships/in/hyderabad after 3 attempts\n2025-05-28 22:58:06,630 - INFO - SUCCESS LetsIntern: 0 internships\n2025-05-28 22:58:06,630 - INFO - Scraping Simplify.jobs...\n2025-05-28 22:58:06,632 - DEBUG - Starting new HTTPS connection (1): simplify.jobs:443\n2025-05-28 22:58:07,448 - DEBUG - https://simplify.jobs:443 \"GET /internships HTTP/1.1\" 200 None\n2025-05-28 22:58:07,637 - INFO - Scraped 0 internships from Simplify.jobs\n2025-05-28 22:58:07,638 - INFO - SUCCESS Simplify.jobs: 0 internships\n2025-05-28 22:58:07,638 - INFO - Total scraped: 6 internships\n2025-05-28 22:58:07,638 - INFO - Stage 2: Applying multi-stage filtering...\n2025-05-28 22:58:07,639 - INFO - Date filter: 6/6 internships (date filtering skipped in simplified format)\n2025-05-28 22:58:07,639 - DEBUG - \\u2705 CSE/IT match: 'software testing' - matched keywords: ['fresher']\n2025-05-28 22:58:07,639 - DEBUG - \\u2705 CSE/IT match: 'software development' - matched keywords: ['software development', 'development', 'fresher']\n2025-05-28 22:58:07,639 - DEBUG - \\u2705 CSE/IT match: 'network engineer intern' - matched keywords: ['engineer', 'intern']\n2025-05-28 22:58:07,640 - DEBUG - \\u2705 CSE/IT match: 'trainee consultant - sap abap job' - matched keywords: ['trainee']\n2025-05-28 22:58:07,640 - DEBUG - \\u2705 CSE/IT match: 'trainee consultant - sap abap job' - matched keywords: ['trainee']\n2025-05-28 22:58:07,640 - DEBUG - \\u2705 CSE/IT match: 'mern stack intern (2024/2025 passedouts only)' - matched keywords: ['intern']\n2025-05-28 22:58:07,641 - INFO - Branch filter results: 6/6 internships match field criteria\n2025-05-28 22:58:07,641 - INFO -   CSE/IT: 6 internships\n2025-05-28 22:58:07,641 - INFO -   EEE/ECE: 0 internships\n2025-05-28 22:58:07,641 - INFO -   MECH: 0 internships\n2025-05-28 22:58:07,641 - INFO - Location filter: 6/6 internships (location filtering skipped - using location-specific URLs)\n2025-05-28 22:58:07,642 - INFO - Deduplication: 4/6 unique internships\n2025-05-28 22:58:07,642 - INFO - Filtering results:\n2025-05-28 22:58:07,642 - INFO -   Total scraped: 6\n2025-05-28 22:58:07,643 - INFO -   After date filter: 6\n2025-05-28 22:58:07,643 - INFO -   After branch filter: 6\n2025-05-28 22:58:07,643 - INFO -   After location filter: 6\n2025-05-28 22:58:07,643 - INFO -   After deduplication: 4\n2025-05-28 22:58:07,644 - INFO - All filtered internships saved to: filtered_internships.json\n2025-05-28 22:58:07,645 - INFO - Stage 3: AI-powered selection...\n2025-05-28 22:58:08,034 - INFO - AI analyzing 4 internships...\n2025-05-28 22:58:08,082 - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-a5b14c0c-e761-4fda-a80a-c5fbdea33197', 'json_data': {'messages': [{'role': 'user', 'content': 'You are a career advisor for engineering students. Analyze these internship opportunities and select the SINGLE most valuable and legitimate one.\\n\\nEvaluation Criteria:\\n1. Relevance to engineering students (especially CSE/IT/AI/ML)\\n2. Company legitimacy (avoid fake companies, MLM schemes, unpaid \"opportunities\")\\n3. Professional posting quality (detailed description, clear requirements)\\n4. Learning potential and career value\\n5. Realistic compensation and expectations\\n\\nInternships to evaluate:\\n[\\n  {\\n    \"number\": 1,\\n    \"title\": \"Network Engineer Intern\",\\n    \"company\": \"Ninternet\",\\n    \"location\": \"\",\\n    \"description\": \"\",\\n    \"source\": \"\"\\n  },\\n  {\\n    \"number\": 2,\\n    \"title\": \"Trainee Consultant - SAP ABAP Job\",\\n    \"company\": \"YASH Technologies\",\\n    \"location\": \"\",\\n    \"description\": \"\",\\n    \"source\": \"\"\\n  },\\n  {\\n    \"number\": 3,\\n    \"title\": \"Trainee Consultant - SAP ABAP Job\",\\n    \"company\": \"YASH Technologies Middle East\",\\n    \"location\": \"\",\\n    \"description\": \"\",\\n    \"source\": \"\"\\n  },\\n  {\\n    \"number\": 4,\\n    \"title\": \"Mern stack Intern (2024/2025 Passedouts only)\",\\n    \"company\": \"techolution\",\\n    \"location\": \"\",\\n    \"description\": \"\",\\n    \"source\": \"\"\\n  }\\n]\\n\\nReturn only the exact title of the selected internship. If all seem suspicious or low-quality, return \"NONE\".'}], 'model': 'llama3-8b-8192', 'max_tokens': 100, 'temperature': 0.1}}\n2025-05-28 22:58:08,194 - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions\n2025-05-28 22:58:08,194 - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None\n2025-05-28 22:58:08,513 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002E80B4A92B0>\n2025-05-28 22:58:08,513 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002E80B4148C0> server_hostname='api.groq.com' timeout=5.0\n2025-05-28 22:58:08,676 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002E80B3E5950>\n2025-05-28 22:58:08,677 - DEBUG - send_request_headers.started request=<Request [b'POST']>\n2025-05-28 22:58:08,677 - DEBUG - send_request_headers.complete\n2025-05-28 22:58:08,678 - DEBUG - send_request_body.started request=<Request [b'POST']>\n2025-05-28 22:58:08,678 - DEBUG - send_request_body.complete\n2025-05-28 22:58:08,678 - DEBUG - receive_response_headers.started request=<Request [b'POST']>\n2025-05-28 22:58:08,835 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 403, b'Forbidden', [(b'Date', b'Wed, 28 May 2025 17:28:08 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'__cf_bm=FVuGzK3nK5bxCmlFAPpSzlNXJgPGafqdRl1k0KLwIR4-1748453288-*******-OovoT4imPNy5hJK7QKs52G.WRCuSgUhjG1kb48QhENCasRRzWcr2BrqKO3h4cJ4omDpWm0XTZkd3DIEeljTK7Yejlip_g9aZDpHs4UFyJ4A; path=/; expires=Wed, 28-May-25 17:58:08 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Vary', b'Accept-Encoding'), (b'Server', b'cloudflare'), (b'CF-RAY', b'946f777bbdc1e5d8-NRT'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=\":443\"; ma=86400')])\n2025-05-28 22:58:08,837 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 403 Forbidden\"\n2025-05-28 22:58:08,837 - DEBUG - receive_response_body.started request=<Request [b'POST']>\n2025-05-28 22:58:09,042 - DEBUG - receive_response_body.complete\n2025-05-28 22:58:09,043 - DEBUG - response_closed.started\n2025-05-28 22:58:09,043 - DEBUG - response_closed.complete\n2025-05-28 22:58:09,043 - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions \"403 Forbidden\" Headers({'date': 'Wed, 28 May 2025 17:28:08 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'set-cookie': '__cf_bm=FVuGzK3nK5bxCmlFAPpSzlNXJgPGafqdRl1k0KLwIR4-1748453288-*******-OovoT4imPNy5hJK7QKs52G.WRCuSgUhjG1kb48QhENCasRRzWcr2BrqKO3h4cJ4omDpWm0XTZkd3DIEeljTK7Yejlip_g9aZDpHs4UFyJ4A; path=/; expires=Wed, 28-May-25 17:58:08 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'vary': 'Accept-Encoding', 'server': 'cloudflare', 'cf-ray': '946f777bbdc1e5d8-NRT', 'content-encoding': 'gzip', 'alt-svc': 'h3=\":443\"; ma=86400'})\n2025-05-28 22:58:09,044 - DEBUG - Encountered httpx.HTTPStatusError\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\.venv\\Lib\\site-packages\\groq\\_base_client.py\", line 1011, in request\n    response.raise_for_status()\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\.venv\\Lib\\site-packages\\httpx\\_models.py\", line 829, in raise_for_status\n    raise HTTPStatusError(message, request=request, response=self)\nhttpx.HTTPStatusError: Client error '403 Forbidden' for url 'https://api.groq.com/openai/v1/chat/completions'\nFor more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/403\n2025-05-28 22:58:09,048 - DEBUG - Not retrying\n2025-05-28 22:58:09,048 - DEBUG - Re-raising status error\n2025-05-28 22:58:09,049 - ERROR - AI selection failed: Error code: 403 - {'error': {'message': 'Access denied. Please check your network settings.'}}\n2025-05-28 22:58:09,050 - INFO - === Internship Scraping Completed ===\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\internship_scraper.py\", line 1385, in <module>\n    print(\"\\U0001f3af INTELLIGENT INTERNSHIP SCRAPER RESULTS\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3af' in position 0: character maps to <undefined>\n2025-05-28 22:58:09,090 - DEBUG - close.started\n2025-05-28 22:58:09,090 - DEBUG - close.complete\n", "timeout": false}, {"script": "jobs_scraper.py", "success": false, "return_code": 1, "execution_time": 35.572632789611816, "stdout": "\n============================================================\n", "stderr": "2025-05-28 22:58:12,407 - INFO - === Starting Recent Entry-Level Job Scraper (24H + 0-1 Years Experience) ===\n2025-05-28 22:58:12,407 - INFO - Stage 1: Scraping jobs from multiple sources...\n2025-05-28 22:58:12,407 - INFO - Scraping LinkedIn with COMPREHENSIVE multi-search strategy...\n2025-05-28 22:58:12,407 - INFO - Search 1/6: computer%20science%20entry%20level\n2025-05-28 22:58:12,412 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:58:13,920 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?distance=25&f_E=2&f_TPR=r86400&geoId=105556991&keywords=computer%20science%20entry%20level&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 15757\n2025-05-28 22:58:14,124 - INFO - Successfully fetched LinkedIn page for search 1\n2025-05-28 22:58:14,128 - DEBUG - Search 1 - Found 2 cards with selector: 'div[data-entity-urn*=\"job\"]'\n2025-05-28 22:58:14,131 - DEBUG - Search 1 - Found 2 cards with selector: 'div.base-card'\n2025-05-28 22:58:14,138 - DEBUG - Search 1 - Found 2 cards with selector: 'div.job-search-card'\n2025-05-28 22:58:14,141 - DEBUG - Search 1 - Found 2 cards with selector: 'div.base-search-card'\n2025-05-28 22:58:14,168 - DEBUG - Search 1 - Found 5 cards with selector: 'div[class*=\"job\"]'\n2025-05-28 22:58:14,171 - INFO - Search 1 - Found 5 total unique job cards to analyze\n2025-05-28 22:58:14,171 - DEBUG - Search 1 - Sample card 1 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:14,171 - DEBUG - Search 1 - Sample card 2 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:14,171 - DEBUG - Search 1 - Processing card 1/5\n2025-05-28 22:58:14,172 - DEBUG - Search 1 - Card 1 contains 'promoted': False\n2025-05-28 22:58:14,172 - DEBUG - Search 1 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:14,172 - DEBUG - Search 1 - \\u2705 Card 1 title extracted: 'Junior Software Engineer (Fresher-Friendly)'\n2025-05-28 22:58:14,173 - DEBUG - Search 1 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:14,173 - DEBUG - Search 1 - \\u2705 Card 1 company extracted: 'Nova'\n2025-05-28 22:58:14,173 - DEBUG - Search 1 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:14,173 - DEBUG - Search 1 - \\u2705 Card 1 URL extracted\n2025-05-28 22:58:14,173 - DEBUG - Found posting date text: '12 hours ago'\n2025-05-28 22:58:14,173 - DEBUG - Search 1 - \\U0001f4c5 POSTING DATE found for 'Junior Software Engineer (Fresher-Friendly)': '12 hours ago'\n2025-05-28 22:58:14,173 - DEBUG - Search 1 - \\u2705 KEEPING 'Junior Software Engineer (Fresher-Friendly)' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:14,174 - DEBUG - Search 1 - \\U0001f389 Card 1 SUCCESSFULLY ADDED: 'Junior Software Engineer (Fresher-Friendly)' at 'Nova'\n2025-05-28 22:58:14,174 - DEBUG - Search 1 - Processing card 2/5\n2025-05-28 22:58:14,174 - DEBUG - Search 1 - Card 2 contains 'promoted': False\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - \\u2705 Card 2 title extracted: 'Cyber Security Analyst'\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - \\u2705 Card 2 company extracted: 'Prudent Technologies and Consulting, Inc.'\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - \\u2705 Card 2 URL extracted\n2025-05-28 22:58:14,175 - DEBUG - Found posting date text: '11 hours ago'\n2025-05-28 22:58:14,175 - DEBUG - Search 1 - \\U0001f4c5 POSTING DATE found for 'Cyber Security Analyst': '11 hours ago'\n2025-05-28 22:58:14,176 - DEBUG - Search 1 - \\u2705 KEEPING 'Cyber Security Analyst' - posted 11 hours ago (within 24 hours)\n2025-05-28 22:58:14,176 - DEBUG - Search 1 - \\U0001f389 Card 2 SUCCESSFULLY ADDED: 'Cyber Security Analyst' at 'Prudent Technologies and Consulting, Inc.'\n2025-05-28 22:58:14,176 - DEBUG - Search 1 - Processing card 3/5\n2025-05-28 22:58:14,176 - DEBUG - Search 1 - Card 3 contains 'promoted': False\n2025-05-28 22:58:14,177 - DEBUG - Search 1 - \\u274c No title found for card 3 - SKIPPING\n2025-05-28 22:58:14,177 - DEBUG - Search 1 - Processing card 4/5\n2025-05-28 22:58:14,178 - DEBUG - Search 1 - Card 4 contains 'promoted': False\n2025-05-28 22:58:14,178 - DEBUG - Search 1 - \\u274c No title found for card 4 - SKIPPING\n2025-05-28 22:58:14,178 - DEBUG - Search 1 - Processing card 5/5\n2025-05-28 22:58:14,178 - DEBUG - Search 1 - Card 5 contains 'promoted': False\n2025-05-28 22:58:14,179 - DEBUG - Search 1 - \\u274c No title found for card 5 - SKIPPING\n2025-05-28 22:58:14,179 - INFO - Search 1 completed: 2 jobs found\n2025-05-28 22:58:14,179 - INFO - Search 2/6: software%20engineer%20junior\n2025-05-28 22:58:14,180 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:58:16,245 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?distance=25&f_E=2&f_TPR=r86400&geoId=105556991&keywords=software%20engineer%20junior&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 25554\n2025-05-28 22:58:16,530 - INFO - Successfully fetched LinkedIn page for search 2\n2025-05-28 22:58:16,540 - DEBUG - Search 2 - Found 60 cards with selector: 'div[data-entity-urn*=\"job\"]'\n2025-05-28 22:58:16,549 - DEBUG - Search 2 - Found 60 cards with selector: 'div.base-card'\n2025-05-28 22:58:16,566 - DEBUG - Search 2 - Found 60 cards with selector: 'div.job-search-card'\n2025-05-28 22:58:16,580 - DEBUG - Search 2 - Found 60 cards with selector: 'div.base-search-card'\n2025-05-28 22:58:16,672 - DEBUG - Search 2 - Found 90 cards with selector: 'div[class*=\"job\"]'\n2025-05-28 22:58:16,681 - INFO - Search 2 - Found 63 total unique job cards to analyze\n2025-05-28 22:58:16,681 - DEBUG - Search 2 - Sample card 1 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:16,682 - DEBUG - Search 2 - Sample card 2 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:16,682 - DEBUG - Search 2 - Processing card 1/63\n2025-05-28 22:58:16,682 - DEBUG - Search 2 - Card 1 contains 'promoted': False\n2025-05-28 22:58:16,682 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - \\u2705 Card 1 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - \\u2705 Card 1 company extracted: 'Capgemini'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - \\u2705 Card 1 URL extracted\n2025-05-28 22:58:16,683 - DEBUG - Found posting date text: '5 hours ago'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '5 hours ago'\n2025-05-28 22:58:16,683 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 5 hours ago (within 24 hours)\n2025-05-28 22:58:16,684 - DEBUG - Search 2 - \\U0001f389 Card 1 SUCCESSFULLY ADDED: 'Software Engineer' at 'Capgemini'\n2025-05-28 22:58:16,684 - DEBUG - Search 2 - Processing card 2/63\n2025-05-28 22:58:16,684 - DEBUG - Search 2 - Card 2 contains 'promoted': False\n2025-05-28 22:58:16,685 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,685 - DEBUG - Search 2 - \\u2705 Card 2 title extracted: 'Junior Software Engineer (Fresher-Friendly)'\n2025-05-28 22:58:16,685 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,685 - DEBUG - Search 2 - \\u2705 Card 2 company extracted: 'Nova'\n2025-05-28 22:58:16,685 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,685 - DEBUG - Search 2 - \\u2705 Card 2 URL extracted\n2025-05-28 22:58:16,685 - DEBUG - Found posting date text: '12 hours ago'\n2025-05-28 22:58:16,686 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Junior Software Engineer (Fresher-Friendly)': '12 hours ago'\n2025-05-28 22:58:16,686 - DEBUG - Search 2 - \\u2705 KEEPING 'Junior Software Engineer (Fresher-Friendly)' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:16,686 - DEBUG - Search 2 - \\U0001f389 Card 2 SUCCESSFULLY ADDED: 'Junior Software Engineer (Fresher-Friendly)' at 'Nova'\n2025-05-28 22:58:16,686 - DEBUG - Search 2 - Processing card 3/63\n2025-05-28 22:58:16,686 - DEBUG - Search 2 - Card 3 contains 'promoted': False\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - \\u2705 Card 3 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - \\u2705 Card 3 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - \\u2705 Card 3 URL extracted\n2025-05-28 22:58:16,687 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,687 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,688 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,688 - DEBUG - Search 2 - \\U0001f389 Card 3 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,688 - DEBUG - Search 2 - Processing card 4/63\n2025-05-28 22:58:16,688 - DEBUG - Search 2 - Card 4 contains 'promoted': False\n2025-05-28 22:58:16,688 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - \\u2705 Card 4 title extracted: 'Developer'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - \\u2705 Card 4 company extracted: 'Cognizant'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - \\u2705 Card 4 URL extracted\n2025-05-28 22:58:16,689 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Developer': '3 hours ago'\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - \\u2705 KEEPING 'Developer' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,689 - DEBUG - Search 2 - \\U0001f389 Card 4 SUCCESSFULLY ADDED: 'Developer' at 'Cognizant'\n2025-05-28 22:58:16,690 - DEBUG - Search 2 - Processing card 5/63\n2025-05-28 22:58:16,690 - DEBUG - Search 2 - Card 5 contains 'promoted': False\n2025-05-28 22:58:16,690 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,690 - DEBUG - Search 2 - \\u2705 Card 5 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - \\u2705 Card 5 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - \\u2705 Card 5 URL extracted\n2025-05-28 22:58:16,691 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - \\U0001f389 Card 5 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,691 - DEBUG - Search 2 - Processing card 6/63\n2025-05-28 22:58:16,692 - DEBUG - Search 2 - Card 6 contains 'promoted': False\n2025-05-28 22:58:16,692 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,692 - DEBUG - Search 2 - \\u2705 Card 6 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,692 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,692 - DEBUG - Search 2 - \\u2705 Card 6 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,693 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,693 - DEBUG - Search 2 - \\u2705 Card 6 URL extracted\n2025-05-28 22:58:16,693 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,693 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,693 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,693 - DEBUG - Search 2 - \\U0001f389 Card 6 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,693 - DEBUG - Search 2 - Processing card 7/63\n2025-05-28 22:58:16,694 - DEBUG - Search 2 - Card 7 contains 'promoted': False\n2025-05-28 22:58:16,694 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,694 - DEBUG - Search 2 - \\u2705 Card 7 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,694 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,694 - DEBUG - Search 2 - \\u2705 Card 7 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,695 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,695 - DEBUG - Search 2 - \\u2705 Card 7 URL extracted\n2025-05-28 22:58:16,695 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,695 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,695 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,695 - DEBUG - Search 2 - \\U0001f389 Card 7 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,695 - DEBUG - Search 2 - Processing card 8/63\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - Card 8 contains 'promoted': False\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - \\u2705 Card 8 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - \\u2705 Card 8 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,696 - DEBUG - Search 2 - \\u2705 Card 8 URL extracted\n2025-05-28 22:58:16,697 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,697 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,697 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,697 - DEBUG - Search 2 - \\U0001f389 Card 8 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,697 - DEBUG - Search 2 - Processing card 9/63\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - Card 9 contains 'promoted': False\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - \\u2705 Card 9 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - \\u2705 Card 9 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,698 - DEBUG - Search 2 - \\u2705 Card 9 URL extracted\n2025-05-28 22:58:16,698 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,699 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,699 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,699 - DEBUG - Search 2 - \\U0001f389 Card 9 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,699 - DEBUG - Search 2 - Processing card 10/63\n2025-05-28 22:58:16,699 - DEBUG - Search 2 - Card 10 contains 'promoted': False\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - \\u2705 Card 10 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - \\u2705 Card 10 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - \\u2705 Card 10 URL extracted\n2025-05-28 22:58:16,700 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,700 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,701 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,701 - DEBUG - Search 2 - \\U0001f389 Card 10 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,701 - DEBUG - Search 2 - Processing card 11/63\n2025-05-28 22:58:16,701 - DEBUG - Search 2 - Card 11 contains 'promoted': False\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - \\u2705 Card 11 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - \\u2705 Card 11 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - \\u2705 Card 11 URL extracted\n2025-05-28 22:58:16,702 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,702 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,703 - DEBUG - Search 2 - \\U0001f389 Card 11 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,703 - DEBUG - Search 2 - Processing card 12/63\n2025-05-28 22:58:16,703 - DEBUG - Search 2 - Card 12 contains 'promoted': False\n2025-05-28 22:58:16,703 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,703 - DEBUG - Search 2 - \\u2705 Card 12 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - \\u2705 Card 12 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - \\u2705 Card 12 URL extracted\n2025-05-28 22:58:16,704 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - \\U0001f389 Card 12 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,704 - DEBUG - Search 2 - Processing card 13/63\n2025-05-28 22:58:16,705 - DEBUG - Search 2 - Card 13 contains 'promoted': False\n2025-05-28 22:58:16,705 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,705 - DEBUG - Search 2 - \\u2705 Card 13 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - \\u2705 Card 13 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - \\u2705 Card 13 URL extracted\n2025-05-28 22:58:16,706 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - \\U0001f389 Card 13 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,706 - DEBUG - Search 2 - Processing card 14/63\n2025-05-28 22:58:16,707 - DEBUG - Search 2 - Card 14 contains 'promoted': False\n2025-05-28 22:58:16,707 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,707 - DEBUG - Search 2 - \\u2705 Card 14 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,707 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,707 - DEBUG - Search 2 - \\u2705 Card 14 company extracted: 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,708 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,708 - DEBUG - Search 2 - \\u2705 Card 14 URL extracted\n2025-05-28 22:58:16,708 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,708 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '22 hours ago'\n2025-05-28 22:58:16,708 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,708 - DEBUG - Search 2 - \\U0001f389 Card 14 SUCCESSFULLY ADDED: 'Software Engineer' at 'JIVA InfoTech Inc.'\n2025-05-28 22:58:16,708 - DEBUG - Search 2 - Processing card 15/63\n2025-05-28 22:58:16,709 - DEBUG - Search 2 - Card 15 contains 'promoted': False\n2025-05-28 22:58:16,709 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,709 - DEBUG - Search 2 - \\u2705 Card 15 title extracted: 'Full Stack Developer'\n2025-05-28 22:58:16,709 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,709 - DEBUG - Search 2 - \\u2705 Card 15 company extracted: 'STAND 8 Technology Consulting'\n2025-05-28 22:58:16,710 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,710 - DEBUG - Search 2 - \\u2705 Card 15 URL extracted\n2025-05-28 22:58:16,710 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,710 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Full Stack Developer': '2 hours ago'\n2025-05-28 22:58:16,710 - DEBUG - Search 2 - \\u2705 KEEPING 'Full Stack Developer' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,710 - DEBUG - Search 2 - \\U0001f389 Card 15 SUCCESSFULLY ADDED: 'Full Stack Developer' at 'STAND 8 Technology Consulting'\n2025-05-28 22:58:16,710 - DEBUG - Search 2 - Processing card 16/63\n2025-05-28 22:58:16,711 - DEBUG - Search 2 - Card 16 contains 'promoted': False\n2025-05-28 22:58:16,711 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,711 - DEBUG - Search 2 - \\u2705 Card 16 title extracted: 'Automation Test Engineer'\n2025-05-28 22:58:16,711 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,711 - DEBUG - Search 2 - \\u2705 Card 16 company extracted: 'Resolver, a Kroll Business'\n2025-05-28 22:58:16,711 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,712 - DEBUG - Search 2 - \\u2705 Card 16 URL extracted\n2025-05-28 22:58:16,712 - DEBUG - Found posting date text: '6 hours ago'\n2025-05-28 22:58:16,712 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Automation Test Engineer': '6 hours ago'\n2025-05-28 22:58:16,712 - DEBUG - Search 2 - \\u2705 KEEPING 'Automation Test Engineer' - posted 6 hours ago (within 24 hours)\n2025-05-28 22:58:16,712 - DEBUG - Search 2 - \\U0001f389 Card 16 SUCCESSFULLY ADDED: 'Automation Test Engineer' at 'Resolver, a Kroll Business'\n2025-05-28 22:58:16,712 - DEBUG - Search 2 - Processing card 17/63\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - Card 17 contains 'promoted': False\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - \\u2705 Card 17 title extracted: 'Python Developer'\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - \\u2705 Card 17 company extracted: 'Algonox Technologies'\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,713 - DEBUG - Search 2 - \\u2705 Card 17 URL extracted\n2025-05-28 22:58:16,713 - DEBUG - Found posting date text: '8 hours ago'\n2025-05-28 22:58:16,714 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Python Developer': '8 hours ago'\n2025-05-28 22:58:16,714 - DEBUG - Search 2 - \\u2705 KEEPING 'Python Developer' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:16,714 - DEBUG - Search 2 - \\U0001f389 Card 17 SUCCESSFULLY ADDED: 'Python Developer' at 'Algonox Technologies'\n2025-05-28 22:58:16,714 - DEBUG - Search 2 - Processing card 18/63\n2025-05-28 22:58:16,714 - DEBUG - Search 2 - Card 18 contains 'promoted': False\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - \\u2705 Card 18 title extracted: 'Test Engineer - L4'\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - \\u2705 Card 18 company extracted: 'Wipro'\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - \\u2705 Card 18 URL extracted\n2025-05-28 22:58:16,715 - DEBUG - Found posting date text: '8 hours ago'\n2025-05-28 22:58:16,715 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Test Engineer - L4': '8 hours ago'\n2025-05-28 22:58:16,716 - DEBUG - Search 2 - \\u2705 KEEPING 'Test Engineer - L4' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:16,716 - DEBUG - Search 2 - \\U0001f389 Card 18 SUCCESSFULLY ADDED: 'Test Engineer - L4' at 'Wipro'\n2025-05-28 22:58:16,716 - DEBUG - Search 2 - Processing card 19/63\n2025-05-28 22:58:16,716 - DEBUG - Search 2 - Card 19 contains 'promoted': False\n2025-05-28 22:58:16,716 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - \\u2705 Card 19 title extracted: 'Developer - L2'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - \\u2705 Card 19 company extracted: 'Wipro'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - \\u2705 Card 19 URL extracted\n2025-05-28 22:58:16,717 - DEBUG - Found posting date text: '12 hours ago'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Developer - L2': '12 hours ago'\n2025-05-28 22:58:16,717 - DEBUG - Search 2 - \\u2705 KEEPING 'Developer - L2' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:16,718 - DEBUG - Search 2 - \\U0001f389 Card 19 SUCCESSFULLY ADDED: 'Developer - L2' at 'Wipro'\n2025-05-28 22:58:16,718 - DEBUG - Search 2 - Processing card 20/63\n2025-05-28 22:58:16,718 - DEBUG - Search 2 - Card 20 contains 'promoted': False\n2025-05-28 22:58:16,718 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - \\u2705 Card 20 title extracted: 'Software Engineer'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - \\u2705 Card 20 company extracted: 'Zoetis'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - \\u2705 Card 20 URL extracted\n2025-05-28 22:58:16,719 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer': '10 hours ago'\n2025-05-28 22:58:16,719 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:16,720 - DEBUG - Search 2 - \\U0001f389 Card 20 SUCCESSFULLY ADDED: 'Software Engineer' at 'Zoetis'\n2025-05-28 22:58:16,720 - DEBUG - Search 2 - Processing card 21/63\n2025-05-28 22:58:16,720 - DEBUG - Search 2 - Card 21 contains 'promoted': False\n2025-05-28 22:58:16,720 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,720 - DEBUG - Search 2 - \\u2705 Card 21 title extracted: 'AI Engineer'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - \\u2705 Card 21 company extracted: 'Anblicks'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - \\u2705 Card 21 URL extracted\n2025-05-28 22:58:16,721 - DEBUG - Found posting date text: '4 hours ago'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'AI Engineer': '4 hours ago'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - \\u2705 KEEPING 'AI Engineer' - posted 4 hours ago (within 24 hours)\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - \\U0001f389 Card 21 SUCCESSFULLY ADDED: 'AI Engineer' at 'Anblicks'\n2025-05-28 22:58:16,721 - DEBUG - Search 2 - Processing card 22/63\n2025-05-28 22:58:16,722 - DEBUG - Search 2 - Card 22 contains 'promoted': False\n2025-05-28 22:58:16,722 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,722 - DEBUG - Search 2 - \\u2705 Card 22 title extracted: 'DevOps Engineer - L4'\n2025-05-28 22:58:16,722 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - \\u2705 Card 22 company extracted: 'Wipro'\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - \\u2705 Card 22 URL extracted\n2025-05-28 22:58:16,723 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'DevOps Engineer - L4': '3 hours ago'\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - \\U0001f389 Card 22 SUCCESSFULLY ADDED: 'DevOps Engineer - L4' at 'Wipro'\n2025-05-28 22:58:16,723 - DEBUG - Search 2 - Processing card 23/63\n2025-05-28 22:58:16,724 - DEBUG - Search 2 - Card 23 contains 'promoted': False\n2025-05-28 22:58:16,724 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,724 - DEBUG - Search 2 - \\u2705 Card 23 title extracted: 'Software Developer'\n2025-05-28 22:58:16,724 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - \\u2705 Card 23 company extracted: 'Zoetis'\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - \\u2705 Card 23 URL extracted\n2025-05-28 22:58:16,725 - DEBUG - Found posting date text: '5 hours ago'\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Developer': '5 hours ago'\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Developer' - posted 5 hours ago (within 24 hours)\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - \\U0001f389 Card 23 SUCCESSFULLY ADDED: 'Software Developer' at 'Zoetis'\n2025-05-28 22:58:16,725 - DEBUG - Search 2 - Processing card 24/63\n2025-05-28 22:58:16,726 - DEBUG - Search 2 - Card 24 contains 'promoted': False\n2025-05-28 22:58:16,726 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,726 - DEBUG - Search 2 - \\u2705 Card 24 title extracted: 'Embedded Linux Field Engineer for Devices/IoT'\n2025-05-28 22:58:16,726 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,726 - DEBUG - Search 2 - \\u2705 Card 24 company extracted: 'Canonical'\n2025-05-28 22:58:16,727 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,727 - DEBUG - Search 2 - \\u2705 Card 24 URL extracted\n2025-05-28 22:58:16,727 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,727 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Embedded Linux Field Engineer for Devices/IoT': '22 hours ago'\n2025-05-28 22:58:16,727 - DEBUG - Search 2 - \\u2705 KEEPING 'Embedded Linux Field Engineer for Devices/IoT' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,727 - DEBUG - Search 2 - \\U0001f389 Card 24 SUCCESSFULLY ADDED: 'Embedded Linux Field Engineer for Devices/IoT' at 'Canonical'\n2025-05-28 22:58:16,727 - DEBUG - Search 2 - Processing card 25/63\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - Card 25 contains 'promoted': False\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - \\u2705 Card 25 title extracted: 'Software Engineering || B3'\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - \\u2705 Card 25 company extracted: 'Trigent Software Inc'\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,728 - DEBUG - Search 2 - \\u2705 Card 25 URL extracted\n2025-05-28 22:58:16,729 - DEBUG - Found posting date text: '59 minutes ago'\n2025-05-28 22:58:16,729 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineering || B3': '59 minutes ago'\n2025-05-28 22:58:16,729 - DEBUG - Search 2 - \\u274c FILTERED OUT 'Software Engineering || B3' - posted 59 minutes ago (outside 24-hour window)\n2025-05-28 22:58:16,729 - DEBUG - Search 2 - Processing card 26/63\n2025-05-28 22:58:16,729 - DEBUG - Search 2 - Card 26 contains 'promoted': False\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - \\u2705 Card 26 title extracted: 'Software Developer - Back End'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - \\u2705 Card 26 company extracted: 'Zoetis'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - \\u2705 Card 26 URL extracted\n2025-05-28 22:58:16,730 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Developer - Back End': '10 hours ago'\n2025-05-28 22:58:16,730 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Developer - Back End' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:16,731 - DEBUG - Search 2 - \\U0001f389 Card 26 SUCCESSFULLY ADDED: 'Software Developer - Back End' at 'Zoetis'\n2025-05-28 22:58:16,731 - DEBUG - Search 2 - Processing card 27/63\n2025-05-28 22:58:16,731 - DEBUG - Search 2 - Card 27 contains 'promoted': False\n2025-05-28 22:58:16,732 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,732 - DEBUG - Search 2 - \\u2705 Card 27 title extracted: 'Test Engineer - L3'\n2025-05-28 22:58:16,732 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,732 - DEBUG - Search 2 - \\u2705 Card 27 company extracted: 'Wipro'\n2025-05-28 22:58:16,732 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,732 - DEBUG - Search 2 - \\u2705 Card 27 URL extracted\n2025-05-28 22:58:16,732 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:58:16,733 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Test Engineer - L3': '10 hours ago'\n2025-05-28 22:58:16,733 - DEBUG - Search 2 - \\u2705 KEEPING 'Test Engineer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:16,733 - DEBUG - Search 2 - \\U0001f389 Card 27 SUCCESSFULLY ADDED: 'Test Engineer - L3' at 'Wipro'\n2025-05-28 22:58:16,733 - DEBUG - Search 2 - Processing card 28/63\n2025-05-28 22:58:16,733 - DEBUG - Search 2 - Card 28 contains 'promoted': False\n2025-05-28 22:58:16,734 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,734 - DEBUG - Search 2 - \\u2705 Card 28 title extracted: 'Embedded Autosar'\n2025-05-28 22:58:16,734 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,734 - DEBUG - Search 2 - \\u2705 Card 28 company extracted: 'Cognizant'\n2025-05-28 22:58:16,734 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,734 - DEBUG - Search 2 - \\u2705 Card 28 URL extracted\n2025-05-28 22:58:16,734 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,735 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Embedded Autosar': '3 hours ago'\n2025-05-28 22:58:16,735 - DEBUG - Search 2 - \\u2705 KEEPING 'Embedded Autosar' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,735 - DEBUG - Search 2 - \\U0001f389 Card 28 SUCCESSFULLY ADDED: 'Embedded Autosar' at 'Cognizant'\n2025-05-28 22:58:16,735 - DEBUG - Search 2 - Processing card 29/63\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - Card 29 contains 'promoted': False\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - \\u2705 Card 29 title extracted: 'Test Engineer - L3'\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - \\u2705 Card 29 company extracted: 'Wipro'\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,736 - DEBUG - Search 2 - \\u2705 Card 29 URL extracted\n2025-05-28 22:58:16,736 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:58:16,737 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Test Engineer - L3': '10 hours ago'\n2025-05-28 22:58:16,737 - DEBUG - Search 2 - \\u2705 KEEPING 'Test Engineer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:16,737 - DEBUG - Search 2 - \\U0001f389 Card 29 SUCCESSFULLY ADDED: 'Test Engineer - L3' at 'Wipro'\n2025-05-28 22:58:16,737 - DEBUG - Search 2 - Processing card 30/63\n2025-05-28 22:58:16,737 - DEBUG - Search 2 - Card 30 contains 'promoted': False\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - \\u2705 Card 30 title extracted: 'Engineer Software Development - II (Google CCAI)'\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - \\u2705 Card 30 company extracted: 'Verizon'\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - \\u2705 Card 30 URL extracted\n2025-05-28 22:58:16,738 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,738 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Engineer Software Development - II (Google CCAI)': '3 hours ago'\n2025-05-28 22:58:16,739 - DEBUG - Search 2 - \\u2705 KEEPING 'Engineer Software Development - II (Google CCAI)' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,739 - DEBUG - Search 2 - \\U0001f389 Card 30 SUCCESSFULLY ADDED: 'Engineer Software Development - II (Google CCAI)' at 'Verizon'\n2025-05-28 22:58:16,739 - DEBUG - Search 2 - Processing card 31/63\n2025-05-28 22:58:16,739 - DEBUG - Search 2 - Card 31 contains 'promoted': False\n2025-05-28 22:58:16,739 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,739 - DEBUG - Search 2 - \\u2705 Card 31 title extracted: 'DevOps Engineer - L4'\n2025-05-28 22:58:16,740 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,740 - DEBUG - Search 2 - \\u2705 Card 31 company extracted: 'Wipro'\n2025-05-28 22:58:16,740 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,740 - DEBUG - Search 2 - \\u2705 Card 31 URL extracted\n2025-05-28 22:58:16,740 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,740 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'DevOps Engineer - L4': '3 hours ago'\n2025-05-28 22:58:16,741 - DEBUG - Search 2 - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,741 - DEBUG - Search 2 - \\U0001f389 Card 31 SUCCESSFULLY ADDED: 'DevOps Engineer - L4' at 'Wipro'\n2025-05-28 22:58:16,741 - DEBUG - Search 2 - Processing card 32/63\n2025-05-28 22:58:16,742 - DEBUG - Search 2 - Card 32 contains 'promoted': False\n2025-05-28 22:58:16,742 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,742 - DEBUG - Search 2 - \\u2705 Card 32 title extracted: 'Full Stack Developer'\n2025-05-28 22:58:16,742 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - \\u2705 Card 32 company extracted: 'Hummingbird Scientific'\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - \\u2705 Card 32 URL extracted\n2025-05-28 22:58:16,743 - DEBUG - Found posting date text: '5 hours ago'\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Full Stack Developer': '5 hours ago'\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - \\u2705 KEEPING 'Full Stack Developer' - posted 5 hours ago (within 24 hours)\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - \\U0001f389 Card 32 SUCCESSFULLY ADDED: 'Full Stack Developer' at 'Hummingbird Scientific'\n2025-05-28 22:58:16,743 - DEBUG - Search 2 - Processing card 33/63\n2025-05-28 22:58:16,744 - DEBUG - Search 2 - Card 33 contains 'promoted': False\n2025-05-28 22:58:16,744 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,744 - DEBUG - Search 2 - \\u2705 Card 33 title extracted: 'Associate Linux Support Engineer'\n2025-05-28 22:58:16,744 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,744 - DEBUG - Search 2 - \\u2705 Card 33 company extracted: 'Canonical'\n2025-05-28 22:58:16,745 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,745 - DEBUG - Search 2 - \\u2705 Card 33 URL extracted\n2025-05-28 22:58:16,745 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,745 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Associate Linux Support Engineer': '22 hours ago'\n2025-05-28 22:58:16,745 - DEBUG - Search 2 - \\u2705 KEEPING 'Associate Linux Support Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,745 - DEBUG - Search 2 - \\U0001f389 Card 33 SUCCESSFULLY ADDED: 'Associate Linux Support Engineer' at 'Canonical'\n2025-05-28 22:58:16,745 - DEBUG - Search 2 - Processing card 34/63\n2025-05-28 22:58:16,746 - DEBUG - Search 2 - Card 34 contains 'promoted': False\n2025-05-28 22:58:16,746 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,746 - DEBUG - Search 2 - \\u2705 Card 34 title extracted: 'DevOps Engineer - L4'\n2025-05-28 22:58:16,746 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,746 - DEBUG - Search 2 - \\u2705 Card 34 company extracted: 'Wipro'\n2025-05-28 22:58:16,746 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,747 - DEBUG - Search 2 - \\u2705 Card 34 URL extracted\n2025-05-28 22:58:16,747 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,747 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'DevOps Engineer - L4': '3 hours ago'\n2025-05-28 22:58:16,747 - DEBUG - Search 2 - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,747 - DEBUG - Search 2 - \\U0001f389 Card 34 SUCCESSFULLY ADDED: 'DevOps Engineer - L4' at 'Wipro'\n2025-05-28 22:58:16,747 - DEBUG - Search 2 - Processing card 35/63\n2025-05-28 22:58:16,748 - DEBUG - Search 2 - Card 35 contains 'promoted': False\n2025-05-28 22:58:16,748 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,748 - DEBUG - Search 2 - \\u2705 Card 35 title extracted: 'DevOps Engineer - L4'\n2025-05-28 22:58:16,748 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,748 - DEBUG - Search 2 - \\u2705 Card 35 company extracted: 'Wipro'\n2025-05-28 22:58:16,748 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,749 - DEBUG - Search 2 - \\u2705 Card 35 URL extracted\n2025-05-28 22:58:16,749 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,749 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'DevOps Engineer - L4': '3 hours ago'\n2025-05-28 22:58:16,749 - DEBUG - Search 2 - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,749 - DEBUG - Search 2 - \\U0001f389 Card 35 SUCCESSFULLY ADDED: 'DevOps Engineer - L4' at 'Wipro'\n2025-05-28 22:58:16,749 - DEBUG - Search 2 - Processing card 36/63\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - Card 36 contains 'promoted': False\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - \\u2705 Card 36 title extracted: 'DevOps Engineer - L4'\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - \\u2705 Card 36 company extracted: 'Wipro'\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,750 - DEBUG - Search 2 - \\u2705 Card 36 URL extracted\n2025-05-28 22:58:16,751 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,751 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'DevOps Engineer - L4': '3 hours ago'\n2025-05-28 22:58:16,751 - DEBUG - Search 2 - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,751 - DEBUG - Search 2 - \\U0001f389 Card 36 SUCCESSFULLY ADDED: 'DevOps Engineer - L4' at 'Wipro'\n2025-05-28 22:58:16,751 - DEBUG - Search 2 - Processing card 37/63\n2025-05-28 22:58:16,752 - DEBUG - Search 2 - Card 37 contains 'promoted': False\n2025-05-28 22:58:16,752 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,753 - DEBUG - Search 2 - \\u2705 Card 37 title extracted: 'IE Engineer I'\n2025-05-28 22:58:16,753 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,753 - DEBUG - Search 2 - \\u2705 Card 37 company extracted: 'RealPage, Inc.'\n2025-05-28 22:58:16,753 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,753 - DEBUG - Search 2 - \\u2705 Card 37 URL extracted\n2025-05-28 22:58:16,753 - DEBUG - Found posting date text: '3 hours ago'\n2025-05-28 22:58:16,753 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'IE Engineer I': '3 hours ago'\n2025-05-28 22:58:16,754 - DEBUG - Search 2 - \\u2705 KEEPING 'IE Engineer I' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:16,754 - DEBUG - Search 2 - \\U0001f389 Card 37 SUCCESSFULLY ADDED: 'IE Engineer I' at 'RealPage, Inc.'\n2025-05-28 22:58:16,754 - DEBUG - Search 2 - Processing card 38/63\n2025-05-28 22:58:16,754 - DEBUG - Search 2 - Card 38 contains 'promoted': False\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - \\u2705 Card 38 title extracted: 'Configurator - L4'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - \\u2705 Card 38 company extracted: 'Wipro'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - \\u2705 Card 38 URL extracted\n2025-05-28 22:58:16,755 - DEBUG - Found posting date text: '23 hours ago'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Configurator - L4': '23 hours ago'\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - \\u2705 KEEPING 'Configurator - L4' - posted 23 hours ago (within 24 hours)\n2025-05-28 22:58:16,755 - DEBUG - Search 2 - \\U0001f389 Card 38 SUCCESSFULLY ADDED: 'Configurator - L4' at 'Wipro'\n2025-05-28 22:58:16,756 - DEBUG - Search 2 - Processing card 39/63\n2025-05-28 22:58:16,756 - DEBUG - Search 2 - Card 39 contains 'promoted': False\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - \\u2705 Card 39 title extracted: 'Software Developer - Five9 Developer'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - \\u2705 Card 39 company extracted: 'Zoetis'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - \\u2705 Card 39 URL extracted\n2025-05-28 22:58:16,757 - DEBUG - Found posting date text: '8 hours ago'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Developer - Five9 Developer': '8 hours ago'\n2025-05-28 22:58:16,757 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Developer - Five9 Developer' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:16,758 - DEBUG - Search 2 - \\U0001f389 Card 39 SUCCESSFULLY ADDED: 'Software Developer - Five9 Developer' at 'Zoetis'\n2025-05-28 22:58:16,758 - DEBUG - Search 2 - Processing card 40/63\n2025-05-28 22:58:16,758 - DEBUG - Search 2 - Card 40 contains 'promoted': False\n2025-05-28 22:58:16,758 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,758 - DEBUG - Search 2 - \\u2705 Card 40 title extracted: 'Full Stack Developer'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - \\u2705 Card 40 company extracted: 'Zoetis'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - \\u2705 Card 40 URL extracted\n2025-05-28 22:58:16,759 - DEBUG - Found posting date text: '8 hours ago'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Full Stack Developer': '8 hours ago'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - \\u2705 KEEPING 'Full Stack Developer' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - \\U0001f389 Card 40 SUCCESSFULLY ADDED: 'Full Stack Developer' at 'Zoetis'\n2025-05-28 22:58:16,759 - DEBUG - Search 2 - Processing card 41/63\n2025-05-28 22:58:16,760 - DEBUG - Search 2 - Card 41 contains 'promoted': False\n2025-05-28 22:58:16,760 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,760 - DEBUG - Search 2 - \\u2705 Card 41 title extracted: 'Developer - L3'\n2025-05-28 22:58:16,760 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - \\u2705 Card 41 company extracted: 'Wipro'\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - \\u2705 Card 41 URL extracted\n2025-05-28 22:58:16,761 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Developer - L3': '10 hours ago'\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - \\u2705 KEEPING 'Developer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - \\U0001f389 Card 41 SUCCESSFULLY ADDED: 'Developer - L3' at 'Wipro'\n2025-05-28 22:58:16,761 - DEBUG - Search 2 - Processing card 42/63\n2025-05-28 22:58:16,762 - DEBUG - Search 2 - Card 42 contains 'promoted': False\n2025-05-28 22:58:16,762 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,762 - DEBUG - Search 2 - \\u2705 Card 42 title extracted: 'Software Fullstack Developer - CMS'\n2025-05-28 22:58:16,762 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,762 - DEBUG - Search 2 - \\u2705 Card 42 company extracted: 'Zoetis'\n2025-05-28 22:58:16,762 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,763 - DEBUG - Search 2 - \\u2705 Card 42 URL extracted\n2025-05-28 22:58:16,763 - DEBUG - Found posting date text: '8 hours ago'\n2025-05-28 22:58:16,763 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Fullstack Developer - CMS': '8 hours ago'\n2025-05-28 22:58:16,763 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Fullstack Developer - CMS' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:16,763 - DEBUG - Search 2 - \\U0001f389 Card 42 SUCCESSFULLY ADDED: 'Software Fullstack Developer - CMS' at 'Zoetis'\n2025-05-28 22:58:16,763 - DEBUG - Search 2 - Processing card 43/63\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - Card 43 contains 'promoted': False\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - \\u2705 Card 43 title extracted: 'Embedded Linux Consultant - Japan'\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - \\u2705 Card 43 company extracted: 'Canonical'\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,764 - DEBUG - Search 2 - \\u2705 Card 43 URL extracted\n2025-05-28 22:58:16,765 - DEBUG - Found posting date text: '22 hours ago'\n2025-05-28 22:58:16,765 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Embedded Linux Consultant - Japan': '22 hours ago'\n2025-05-28 22:58:16,765 - DEBUG - Search 2 - \\u2705 KEEPING 'Embedded Linux Consultant - Japan' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:16,765 - DEBUG - Search 2 - \\U0001f389 Card 43 SUCCESSFULLY ADDED: 'Embedded Linux Consultant - Japan' at 'Canonical'\n2025-05-28 22:58:16,765 - DEBUG - Search 2 - Processing card 44/63\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - Card 44 contains 'promoted': False\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - \\u2705 Card 44 title extracted: 'Data Platform Engineer (HPC Applications)'\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - \\u2705 Card 44 company extracted: 'Sanofi'\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,766 - DEBUG - Search 2 - \\u2705 Card 44 URL extracted\n2025-05-28 22:58:16,766 - DEBUG - Found posting date text: '21 hours ago'\n2025-05-28 22:58:16,767 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Data Platform Engineer (HPC Applications)': '21 hours ago'\n2025-05-28 22:58:16,767 - DEBUG - Search 2 - \\u2705 KEEPING 'Data Platform Engineer (HPC Applications)' - posted 21 hours ago (within 24 hours)\n2025-05-28 22:58:16,767 - DEBUG - Search 2 - \\U0001f389 Card 44 SUCCESSFULLY ADDED: 'Data Platform Engineer (HPC Applications)' at 'Sanofi'\n2025-05-28 22:58:16,767 - DEBUG - Search 2 - Processing card 45/63\n2025-05-28 22:58:16,767 - DEBUG - Search 2 - Card 45 contains 'promoted': False\n2025-05-28 22:58:16,768 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,768 - DEBUG - Search 2 - \\u2705 Card 45 title extracted: 'Software Engineer - React Native'\n2025-05-28 22:58:16,768 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,768 - DEBUG - Search 2 - \\u2705 Card 45 company extracted: 'Optum'\n2025-05-28 22:58:16,768 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,768 - DEBUG - Search 2 - \\u2705 Card 45 URL extracted\n2025-05-28 22:58:16,768 - DEBUG - Found posting date text: '17 hours ago'\n2025-05-28 22:58:16,769 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Software Engineer - React Native': '17 hours ago'\n2025-05-28 22:58:16,769 - DEBUG - Search 2 - \\u2705 KEEPING 'Software Engineer - React Native' - posted 17 hours ago (within 24 hours)\n2025-05-28 22:58:16,769 - DEBUG - Search 2 - \\U0001f389 Card 45 SUCCESSFULLY ADDED: 'Software Engineer - React Native' at 'Optum'\n2025-05-28 22:58:16,769 - DEBUG - Search 2 - Processing card 46/63\n2025-05-28 22:58:16,769 - DEBUG - Search 2 - Card 46 contains 'promoted': False\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - \\u2705 Card 46 title extracted: 'React with PHP (any framework) Developer'\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - \\u2705 Card 46 company extracted: 'Talentifyy'\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - \\u2705 Card 46 URL extracted\n2025-05-28 22:58:16,770 - DEBUG - Found posting date text: '4 hours ago'\n2025-05-28 22:58:16,770 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'React with PHP (any framework) Developer': '4 hours ago'\n2025-05-28 22:58:16,771 - DEBUG - Search 2 - \\u2705 KEEPING 'React with PHP (any framework) Developer' - posted 4 hours ago (within 24 hours)\n2025-05-28 22:58:16,771 - DEBUG - Search 2 - \\U0001f389 Card 46 SUCCESSFULLY ADDED: 'React with PHP (any framework) Developer' at 'Talentifyy'\n2025-05-28 22:58:16,771 - DEBUG - Search 2 - Processing card 47/63\n2025-05-28 22:58:16,771 - DEBUG - Search 2 - Card 47 contains 'promoted': False\n2025-05-28 22:58:16,771 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - \\u2705 Card 47 title extracted: 'IN-Software Engineer .Net, ASP.Net Core, Angular, ReactJS'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - \\u2705 Card 47 company extracted: 'Blue Yonder'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - \\u2705 Card 47 URL extracted\n2025-05-28 22:58:16,772 - DEBUG - Found posting date text: '16 hours ago'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'IN-Software Engineer .Net, ASP.Net Core, Angular, ReactJS': '16 hours ago'\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - \\u2705 KEEPING 'IN-Software Engineer .Net, ASP.Net Core, Angular, ReactJS' - posted 16 hours ago (within 24 hours)\n2025-05-28 22:58:16,772 - DEBUG - Search 2 - \\U0001f389 Card 47 SUCCESSFULLY ADDED: 'IN-Software Engineer .Net, ASP.Net Core, Angular, ReactJS' at 'Blue Yonder'\n2025-05-28 22:58:16,773 - DEBUG - Search 2 - Processing card 48/63\n2025-05-28 22:58:16,773 - DEBUG - Search 2 - Card 48 contains 'promoted': False\n2025-05-28 22:58:16,773 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,773 - DEBUG - Search 2 - \\u2705 Card 48 title extracted: 'Remote Fullstack Developer - 17853'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - \\u2705 Card 48 company extracted: 'Turing'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - \\u2705 Card 48 URL extracted\n2025-05-28 22:58:16,774 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Developer - 17853': '2 hours ago'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - \\U0001f389 Card 48 SUCCESSFULLY ADDED: 'Remote Fullstack Developer - 17853' at 'Turing'\n2025-05-28 22:58:16,774 - DEBUG - Search 2 - Processing card 49/63\n2025-05-28 22:58:16,775 - DEBUG - Search 2 - Card 49 contains 'promoted': False\n2025-05-28 22:58:16,775 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,775 - DEBUG - Search 2 - \\u2705 Card 49 title extracted: 'Remote Fullstack Developer - 17853'\n2025-05-28 22:58:16,775 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - \\u2705 Card 49 company extracted: 'Turing'\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - \\u2705 Card 49 URL extracted\n2025-05-28 22:58:16,776 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Developer - 17853': '2 hours ago'\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - \\U0001f389 Card 49 SUCCESSFULLY ADDED: 'Remote Fullstack Developer - 17853' at 'Turing'\n2025-05-28 22:58:16,776 - DEBUG - Search 2 - Processing card 50/63\n2025-05-28 22:58:16,777 - DEBUG - Search 2 - Card 50 contains 'promoted': False\n2025-05-28 22:58:16,777 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,777 - DEBUG - Search 2 - \\u2705 Card 50 title extracted: 'Remote Fullstack Developer - 17853'\n2025-05-28 22:58:16,777 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,777 - DEBUG - Search 2 - \\u2705 Card 50 company extracted: 'Turing'\n2025-05-28 22:58:16,778 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,778 - DEBUG - Search 2 - \\u2705 Card 50 URL extracted\n2025-05-28 22:58:16,778 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,778 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Developer - 17853': '2 hours ago'\n2025-05-28 22:58:16,778 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,778 - DEBUG - Search 2 - \\U0001f389 Card 50 SUCCESSFULLY ADDED: 'Remote Fullstack Developer - 17853' at 'Turing'\n2025-05-28 22:58:16,778 - DEBUG - Search 2 - Processing card 51/63\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - Card 51 contains 'promoted': False\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - \\u2705 Card 51 title extracted: 'Remote Fullstack Developer - 17853'\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - \\u2705 Card 51 company extracted: 'Turing'\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,779 - DEBUG - Search 2 - \\u2705 Card 51 URL extracted\n2025-05-28 22:58:16,780 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,780 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Developer - 17853': '2 hours ago'\n2025-05-28 22:58:16,780 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,780 - DEBUG - Search 2 - \\U0001f389 Card 51 SUCCESSFULLY ADDED: 'Remote Fullstack Developer - 17853' at 'Turing'\n2025-05-28 22:58:16,780 - DEBUG - Search 2 - Processing card 52/63\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - Card 52 contains 'promoted': False\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - \\u2705 Card 52 title extracted: 'Remote Fullstack Developer - 17853'\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - \\u2705 Card 52 company extracted: 'Turing'\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - \\u2705 Card 52 URL extracted\n2025-05-28 22:58:16,781 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,781 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Developer - 17853': '2 hours ago'\n2025-05-28 22:58:16,782 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,782 - DEBUG - Search 2 - \\U0001f389 Card 52 SUCCESSFULLY ADDED: 'Remote Fullstack Developer - 17853' at 'Turing'\n2025-05-28 22:58:16,782 - DEBUG - Search 2 - Processing card 53/63\n2025-05-28 22:58:16,782 - DEBUG - Search 2 - Card 53 contains 'promoted': False\n2025-05-28 22:58:16,782 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - \\u2705 Card 53 title extracted: 'Remote Fullstack Developer - 17853'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - \\u2705 Card 53 company extracted: 'Turing'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - \\u2705 Card 53 URL extracted\n2025-05-28 22:58:16,783 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Developer - 17853': '2 hours ago'\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,783 - DEBUG - Search 2 - \\U0001f389 Card 53 SUCCESSFULLY ADDED: 'Remote Fullstack Developer - 17853' at 'Turing'\n2025-05-28 22:58:16,784 - DEBUG - Search 2 - Processing card 54/63\n2025-05-28 22:58:16,784 - DEBUG - Search 2 - Card 54 contains 'promoted': False\n2025-05-28 22:58:16,784 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,785 - DEBUG - Search 2 - \\u2705 Card 54 title extracted: 'Developer - L3'\n2025-05-28 22:58:16,785 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,785 - DEBUG - Search 2 - \\u2705 Card 54 company extracted: 'Wipro'\n2025-05-28 22:58:16,785 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,785 - DEBUG - Search 2 - \\u2705 Card 54 URL extracted\n2025-05-28 22:58:16,785 - DEBUG - Found posting date text: '12 hours ago'\n2025-05-28 22:58:16,785 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Developer - L3': '12 hours ago'\n2025-05-28 22:58:16,786 - DEBUG - Search 2 - \\u2705 KEEPING 'Developer - L3' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:16,786 - DEBUG - Search 2 - \\U0001f389 Card 54 SUCCESSFULLY ADDED: 'Developer - L3' at 'Wipro'\n2025-05-28 22:58:16,786 - DEBUG - Search 2 - Processing card 55/63\n2025-05-28 22:58:16,786 - DEBUG - Search 2 - Card 55 contains 'promoted': False\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - \\u2705 Card 55 title extracted: 'Remote Fullstack Engineer - 17853'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - \\u2705 Card 55 company extracted: 'Turing'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - \\u2705 Card 55 URL extracted\n2025-05-28 22:58:16,787 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Engineer - 17853': '2 hours ago'\n2025-05-28 22:58:16,787 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,788 - DEBUG - Search 2 - \\U0001f389 Card 55 SUCCESSFULLY ADDED: 'Remote Fullstack Engineer - 17853' at 'Turing'\n2025-05-28 22:58:16,788 - DEBUG - Search 2 - Processing card 56/63\n2025-05-28 22:58:16,788 - DEBUG - Search 2 - Card 56 contains 'promoted': False\n2025-05-28 22:58:16,788 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - \\u2705 Card 56 title extracted: 'Remote Fullstack Engineer - 17853'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - \\u2705 Card 56 company extracted: 'Turing'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - \\u2705 Card 56 URL extracted\n2025-05-28 22:58:16,789 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Engineer - 17853': '2 hours ago'\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,789 - DEBUG - Search 2 - \\U0001f389 Card 56 SUCCESSFULLY ADDED: 'Remote Fullstack Engineer - 17853' at 'Turing'\n2025-05-28 22:58:16,790 - DEBUG - Search 2 - Processing card 57/63\n2025-05-28 22:58:16,790 - DEBUG - Search 2 - Card 57 contains 'promoted': False\n2025-05-28 22:58:16,790 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,790 - DEBUG - Search 2 - \\u2705 Card 57 title extracted: 'Remote Fullstack Engineer - 17853'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - \\u2705 Card 57 company extracted: 'Turing'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - \\u2705 Card 57 URL extracted\n2025-05-28 22:58:16,791 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Engineer - 17853': '2 hours ago'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - \\U0001f389 Card 57 SUCCESSFULLY ADDED: 'Remote Fullstack Engineer - 17853' at 'Turing'\n2025-05-28 22:58:16,791 - DEBUG - Search 2 - Processing card 58/63\n2025-05-28 22:58:16,792 - DEBUG - Search 2 - Card 58 contains 'promoted': False\n2025-05-28 22:58:16,792 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,792 - DEBUG - Search 2 - \\u2705 Card 58 title extracted: 'Remote Fullstack Engineer - 17853'\n2025-05-28 22:58:16,792 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - \\u2705 Card 58 company extracted: 'Turing'\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - \\u2705 Card 58 URL extracted\n2025-05-28 22:58:16,793 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Engineer - 17853': '2 hours ago'\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - \\U0001f389 Card 58 SUCCESSFULLY ADDED: 'Remote Fullstack Engineer - 17853' at 'Turing'\n2025-05-28 22:58:16,793 - DEBUG - Search 2 - Processing card 59/63\n2025-05-28 22:58:16,794 - DEBUG - Search 2 - Card 59 contains 'promoted': False\n2025-05-28 22:58:16,794 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,794 - DEBUG - Search 2 - \\u2705 Card 59 title extracted: 'Remote Fullstack Engineer - 17853'\n2025-05-28 22:58:16,794 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,794 - DEBUG - Search 2 - \\u2705 Card 59 company extracted: 'Turing'\n2025-05-28 22:58:16,794 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,795 - DEBUG - Search 2 - \\u2705 Card 59 URL extracted\n2025-05-28 22:58:16,795 - DEBUG - Found posting date text: '2 hours ago'\n2025-05-28 22:58:16,795 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Remote Fullstack Engineer - 17853': '2 hours ago'\n2025-05-28 22:58:16,795 - DEBUG - Search 2 - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:16,795 - DEBUG - Search 2 - \\U0001f389 Card 59 SUCCESSFULLY ADDED: 'Remote Fullstack Engineer - 17853' at 'Turing'\n2025-05-28 22:58:16,795 - DEBUG - Search 2 - Processing card 60/63\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - Card 60 contains 'promoted': False\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - \\u2705 Card 60 title extracted: 'Developer - L3'\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - \\u2705 Card 60 company extracted: 'Wipro'\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:16,796 - DEBUG - Search 2 - \\u2705 Card 60 URL extracted\n2025-05-28 22:58:16,797 - DEBUG - Found posting date text: '10 hours ago'\n2025-05-28 22:58:16,797 - DEBUG - Search 2 - \\U0001f4c5 POSTING DATE found for 'Developer - L3': '10 hours ago'\n2025-05-28 22:58:16,797 - DEBUG - Search 2 - \\u2705 KEEPING 'Developer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:16,797 - DEBUG - Search 2 - \\U0001f389 Card 60 SUCCESSFULLY ADDED: 'Developer - L3' at 'Wipro'\n2025-05-28 22:58:16,797 - DEBUG - Search 2 - Processing card 61/63\n2025-05-28 22:58:16,797 - DEBUG - Search 2 - Card 61 contains 'promoted': False\n2025-05-28 22:58:16,798 - DEBUG - Search 2 - \\u274c No title found for card 61 - SKIPPING\n2025-05-28 22:58:16,798 - DEBUG - Search 2 - Processing card 62/63\n2025-05-28 22:58:16,798 - DEBUG - Search 2 - Card 62 contains 'promoted': False\n2025-05-28 22:58:16,798 - DEBUG - Search 2 - \\u274c No title found for card 62 - SKIPPING\n2025-05-28 22:58:16,798 - DEBUG - Search 2 - Processing card 63/63\n2025-05-28 22:58:16,799 - DEBUG - Search 2 - Card 63 contains 'promoted': False\n2025-05-28 22:58:16,799 - DEBUG - Search 2 - \\u274c No title found for card 63 - SKIPPING\n2025-05-28 22:58:16,799 - INFO - Search 2 completed: 59 jobs found\n2025-05-28 22:58:16,799 - INFO - Search 3/6: fresh%20graduate%20engineer\n2025-05-28 22:58:16,800 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:58:18,285 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?distance=25&f_E=2&f_TPR=r86400&geoId=105556991&keywords=fresh%20graduate%20engineer&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 15185\n2025-05-28 22:58:18,379 - INFO - Successfully fetched LinkedIn page for search 3\n2025-05-28 22:58:18,427 - INFO - Search 3 - Found 0 total unique job cards to analyze\n2025-05-28 22:58:18,427 - WARNING - Search 3 - No job cards found with any selector\n2025-05-28 22:58:18,427 - INFO - Search 3 completed: 0 jobs found\n2025-05-28 22:58:18,427 - INFO - Search 4/6: associate%20developer\n2025-05-28 22:58:18,428 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:58:19,943 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?distance=25&f_E=2&f_TPR=r86400&geoId=105556991&keywords=associate%20developer&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 15432\n2025-05-28 22:58:20,057 - INFO - Successfully fetched LinkedIn page for search 4\n2025-05-28 22:58:20,103 - INFO - Search 4 - Found 0 total unique job cards to analyze\n2025-05-28 22:58:20,103 - WARNING - Search 4 - No job cards found with any selector\n2025-05-28 22:58:20,103 - INFO - Search 4 completed: 0 jobs found\n2025-05-28 22:58:20,103 - INFO - Search 5/6: junior%20developer\n2025-05-28 22:58:20,104 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:58:21,388 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?distance=25&f_E=2&f_TPR=r86400&geoId=105556991&keywords=junior%20developer&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 17286\n2025-05-28 22:58:21,568 - INFO - Successfully fetched LinkedIn page for search 5\n2025-05-28 22:58:21,573 - DEBUG - Search 5 - Found 2 cards with selector: 'div[data-entity-urn*=\"job\"]'\n2025-05-28 22:58:21,577 - DEBUG - Search 5 - Found 2 cards with selector: 'div.base-card'\n2025-05-28 22:58:21,584 - DEBUG - Search 5 - Found 2 cards with selector: 'div.job-search-card'\n2025-05-28 22:58:21,589 - DEBUG - Search 5 - Found 2 cards with selector: 'div.base-search-card'\n2025-05-28 22:58:21,623 - DEBUG - Search 5 - Found 4 cards with selector: 'div[class*=\"job\"]'\n2025-05-28 22:58:21,627 - INFO - Search 5 - Found 4 total unique job cards to analyze\n2025-05-28 22:58:21,627 - DEBUG - Search 5 - Sample card 1 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:21,627 - DEBUG - Search 5 - Sample card 2 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:21,627 - DEBUG - Search 5 - Processing card 1/4\n2025-05-28 22:58:21,627 - DEBUG - Search 5 - Card 1 contains 'promoted': False\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - \\u2705 Card 1 title extracted: 'Junior Software Engineer (Fresher-Friendly)'\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - \\u2705 Card 1 company extracted: 'Nova'\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - \\u2705 Card 1 URL extracted\n2025-05-28 22:58:21,628 - DEBUG - Found posting date text: '12 hours ago'\n2025-05-28 22:58:21,628 - DEBUG - Search 5 - \\U0001f4c5 POSTING DATE found for 'Junior Software Engineer (Fresher-Friendly)': '12 hours ago'\n2025-05-28 22:58:21,629 - DEBUG - Search 5 - \\u2705 KEEPING 'Junior Software Engineer (Fresher-Friendly)' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:21,629 - DEBUG - Search 5 - \\U0001f389 Card 1 SUCCESSFULLY ADDED: 'Junior Software Engineer (Fresher-Friendly)' at 'Nova'\n2025-05-28 22:58:21,629 - DEBUG - Search 5 - Processing card 2/4\n2025-05-28 22:58:21,629 - DEBUG - Search 5 - Card 2 contains 'promoted': False\n2025-05-28 22:58:21,629 - DEBUG - Search 5 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - \\u2705 Card 2 title extracted: 'React with PHP (any framework) Developer'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - \\u2705 Card 2 company extracted: 'Talentifyy'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - \\u2705 Card 2 URL extracted\n2025-05-28 22:58:21,630 - DEBUG - Found posting date text: '4 hours ago'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - \\U0001f4c5 POSTING DATE found for 'React with PHP (any framework) Developer': '4 hours ago'\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - \\u2705 KEEPING 'React with PHP (any framework) Developer' - posted 4 hours ago (within 24 hours)\n2025-05-28 22:58:21,630 - DEBUG - Search 5 - \\U0001f389 Card 2 SUCCESSFULLY ADDED: 'React with PHP (any framework) Developer' at 'Talentifyy'\n2025-05-28 22:58:21,631 - DEBUG - Search 5 - Processing card 3/4\n2025-05-28 22:58:21,631 - DEBUG - Search 5 - Card 3 contains 'promoted': False\n2025-05-28 22:58:21,631 - DEBUG - Search 5 - \\u274c No title found for card 3 - SKIPPING\n2025-05-28 22:58:21,632 - DEBUG - Search 5 - Processing card 4/4\n2025-05-28 22:58:21,632 - DEBUG - Search 5 - Card 4 contains 'promoted': False\n2025-05-28 22:58:21,632 - DEBUG - Search 5 - \\u274c No title found for card 4 - SKIPPING\n2025-05-28 22:58:21,632 - INFO - Search 5 completed: 2 jobs found\n2025-05-28 22:58:21,632 - INFO - Search 6/6: entry%20level%20full%20stack\n2025-05-28 22:58:21,633 - DEBUG - Starting new HTTPS connection (1): www.linkedin.com:443\n2025-05-28 22:58:23,163 - DEBUG - https://www.linkedin.com:443 \"GET /jobs/search/?distance=25&f_E=2&f_TPR=r86400&geoId=105556991&keywords=entry%20level%20full%20stack&origin=JOB_SEARCH_PAGE_JOB_FILTER HTTP/1.1\" 200 15320\n2025-05-28 22:58:23,276 - INFO - Successfully fetched LinkedIn page for search 6\n2025-05-28 22:58:23,279 - DEBUG - Search 6 - Found 1 cards with selector: 'div[data-entity-urn*=\"job\"]'\n2025-05-28 22:58:23,282 - DEBUG - Search 6 - Found 1 cards with selector: 'div.base-card'\n2025-05-28 22:58:23,289 - DEBUG - Search 6 - Found 1 cards with selector: 'div.job-search-card'\n2025-05-28 22:58:23,292 - DEBUG - Search 6 - Found 1 cards with selector: 'div.base-search-card'\n2025-05-28 22:58:23,318 - DEBUG - Search 6 - Found 3 cards with selector: 'div[class*=\"job\"]'\n2025-05-28 22:58:23,321 - INFO - Search 6 - Found 3 total unique job cards to analyze\n2025-05-28 22:58:23,321 - DEBUG - Search 6 - Sample card 1 structure: tag=div, classes=['base-card', 'relative', 'w-full', 'hover:no-underline', 'focus:no-underline', 'base-card--link', 'base-search-card', 'base-search-card--link', 'job-search-card']\n2025-05-28 22:58:23,321 - DEBUG - Search 6 - Sample card 2 structure: tag=div, classes=['job-alert-redirect-section__container']\n2025-05-28 22:58:23,321 - DEBUG - Search 6 - Processing card 1/3\n2025-05-28 22:58:23,322 - DEBUG - Search 6 - Card 1 contains 'promoted': False\n2025-05-28 22:58:23,322 - DEBUG - Search 6 - Found title with selector: 'h3.base-search-card__title'\n2025-05-28 22:58:23,322 - DEBUG - Search 6 - \\u2705 Card 1 title extracted: 'Junior Software Engineer (Fresher-Friendly)'\n2025-05-28 22:58:23,322 - DEBUG - Search 6 - Found company with selector: 'h4.base-search-card__subtitle'\n2025-05-28 22:58:23,322 - DEBUG - Search 6 - \\u2705 Card 1 company extracted: 'Nova'\n2025-05-28 22:58:23,322 - DEBUG - Search 6 - Found URL with selector: 'a.base-card__full-link'\n2025-05-28 22:58:23,323 - DEBUG - Search 6 - \\u2705 Card 1 URL extracted\n2025-05-28 22:58:23,323 - DEBUG - Found posting date text: '12 hours ago'\n2025-05-28 22:58:23,323 - DEBUG - Search 6 - \\U0001f4c5 POSTING DATE found for 'Junior Software Engineer (Fresher-Friendly)': '12 hours ago'\n2025-05-28 22:58:23,323 - DEBUG - Search 6 - \\u2705 KEEPING 'Junior Software Engineer (Fresher-Friendly)' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:23,323 - DEBUG - Search 6 - \\U0001f389 Card 1 SUCCESSFULLY ADDED: 'Junior Software Engineer (Fresher-Friendly)' at 'Nova'\n2025-05-28 22:58:23,323 - DEBUG - Search 6 - Processing card 2/3\n2025-05-28 22:58:23,323 - DEBUG - Search 6 - Card 2 contains 'promoted': False\n2025-05-28 22:58:23,324 - DEBUG - Search 6 - \\u274c No title found for card 2 - SKIPPING\n2025-05-28 22:58:23,324 - DEBUG - Search 6 - Processing card 3/3\n2025-05-28 22:58:23,324 - DEBUG - Search 6 - Card 3 contains 'promoted': False\n2025-05-28 22:58:23,325 - DEBUG - Search 6 - \\u274c No title found for card 3 - SKIPPING\n2025-05-28 22:58:23,325 - INFO - Search 6 completed: 1 jobs found\n2025-05-28 22:58:23,328 - INFO - LinkedIn COMPREHENSIVE search results:\n2025-05-28 22:58:23,328 - INFO -   Total jobs from all searches: 64\n2025-05-28 22:58:23,328 - INFO -   After deduplication: 60\n2025-05-28 22:58:23,328 - INFO - Sample 1: Junior Software Engineer (Fresher-Friendly) at Nova\n2025-05-28 22:58:23,328 - INFO - Sample 2: Cyber Security Analyst at Prudent Technologies and Consulting, Inc.\n2025-05-28 22:58:23,328 - INFO - Sample 3: Software Engineer at Capgemini\n2025-05-28 22:58:23,328 - INFO - Sample 4: Software Engineer at JIVA InfoTech Inc.\n2025-05-28 22:58:23,328 - INFO - Sample 5: Developer at Cognizant\n2025-05-28 22:58:23,329 - INFO - SUCCESS LinkedIn: 60 jobs\n2025-05-28 22:58:23,329 - INFO - Scraping Naukri.com for additional job coverage...\n2025-05-28 22:58:23,329 - INFO - Naukri search 1/3: software-engineer-jobs-in-hyderabad\n2025-05-28 22:58:23,330 - DEBUG - Starting new HTTPS connection (1): www.naukri.com:443\n2025-05-28 22:58:25,527 - DEBUG - https://www.naukri.com:443 \"GET /software-engineer-jobs-in-hyderabad HTTP/1.1\" 301 71\n2025-05-28 22:58:25,909 - DEBUG - https://www.naukri.com:443 \"GET /software-engineer-jobs-in-hyderabad-secunderabad HTTP/1.1\" 200 5770\n2025-05-28 22:58:26,066 - INFO - Successfully fetched Naukri page for search 1\n2025-05-28 22:58:26,073 - INFO - Naukri search 1 - Found 0 unique job cards\n2025-05-28 22:58:26,073 - INFO - Naukri search 1 completed: 0 jobs found\n2025-05-28 22:58:26,074 - INFO - Naukri search 2/3: computer-science-jobs-in-hyderabad\n2025-05-28 22:58:26,075 - DEBUG - Starting new HTTPS connection (1): www.naukri.com:443\n2025-05-28 22:58:26,915 - DEBUG - https://www.naukri.com:443 \"GET /computer-science-jobs-in-hyderabad HTTP/1.1\" 301 70\n2025-05-28 22:58:27,340 - DEBUG - https://www.naukri.com:443 \"GET /computer-science-jobs-in-hyderabad-secunderabad HTTP/1.1\" 200 5775\n2025-05-28 22:58:27,495 - INFO - Successfully fetched Naukri page for search 2\n2025-05-28 22:58:27,501 - INFO - Naukri search 2 - Found 0 unique job cards\n2025-05-28 22:58:27,501 - INFO - Naukri search 2 completed: 0 jobs found\n2025-05-28 22:58:27,501 - INFO - Naukri search 3/3: full-stack-developer-jobs-in-hyderabad\n2025-05-28 22:58:27,504 - DEBUG - Starting new HTTPS connection (1): www.naukri.com:443\n2025-05-28 22:58:28,354 - DEBUG - https://www.naukri.com:443 \"GET /full-stack-developer-jobs-in-hyderabad HTTP/1.1\" 301 74\n2025-05-28 22:58:28,685 - DEBUG - https://www.naukri.com:443 \"GET /full-stack-developer-jobs-in-hyderabad-secunderabad HTTP/1.1\" 200 6115\n2025-05-28 22:58:28,716 - INFO - Successfully fetched Naukri page for search 3\n2025-05-28 22:58:28,722 - INFO - Naukri search 3 - Found 0 unique job cards\n2025-05-28 22:58:28,723 - INFO - Naukri search 3 completed: 0 jobs found\n2025-05-28 22:58:28,723 - INFO - Naukri.com results: 0 unique jobs\n2025-05-28 22:58:28,724 - INFO - SUCCESS Naukri: 0 jobs\n2025-05-28 22:58:28,724 - INFO - Scraping Indeed.com for additional job coverage...\n2025-05-28 22:58:28,724 - INFO - Indeed search 1/3\n2025-05-28 22:58:28,726 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:29,569 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=software+engineer&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:29,730 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=software+engineer&l=Hyderabad (attempt 1/3)\n2025-05-28 22:58:30,732 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:31,441 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=software+engineer&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:31,596 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=software+engineer&l=Hyderabad (attempt 2/3)\n2025-05-28 22:58:33,599 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:34,294 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=software+engineer&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:34,447 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=software+engineer&l=Hyderabad (attempt 3/3)\n2025-05-28 22:58:34,448 - ERROR - Failed to fetch https://in.indeed.com/jobs?q=software+engineer&l=Hyderabad after 3 attempts\n2025-05-28 22:58:34,448 - WARNING - Failed to get response from Indeed for search 1\n2025-05-28 22:58:34,448 - INFO - Indeed search 2/3\n2025-05-28 22:58:34,450 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:35,162 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=computer+science&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:35,317 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=computer+science&l=Hyderabad (attempt 1/3)\n2025-05-28 22:58:36,321 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:36,868 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=computer+science&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:37,029 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=computer+science&l=Hyderabad (attempt 2/3)\n2025-05-28 22:58:39,031 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:39,537 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=computer+science&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:39,704 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=computer+science&l=Hyderabad (attempt 3/3)\n2025-05-28 22:58:39,705 - ERROR - Failed to fetch https://in.indeed.com/jobs?q=computer+science&l=Hyderabad after 3 attempts\n2025-05-28 22:58:39,705 - WARNING - Failed to get response from Indeed for search 2\n2025-05-28 22:58:39,705 - INFO - Indeed search 3/3\n2025-05-28 22:58:39,707 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:40,415 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=full+stack+developer&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:40,566 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=full+stack+developer&l=Hyderabad (attempt 1/3)\n2025-05-28 22:58:41,569 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:42,267 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=full+stack+developer&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:42,429 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=full+stack+developer&l=Hyderabad (attempt 2/3)\n2025-05-28 22:58:44,431 - DEBUG - Starting new HTTPS connection (1): in.indeed.com:443\n2025-05-28 22:58:44,997 - DEBUG - https://in.indeed.com:443 \"GET /jobs?q=full+stack+developer&l=Hyderabad HTTP/1.1\" 403 None\n2025-05-28 22:58:45,150 - WARNING - HTTP error 403 for https://in.indeed.com/jobs?q=full+stack+developer&l=Hyderabad (attempt 3/3)\n2025-05-28 22:58:45,151 - ERROR - Failed to fetch https://in.indeed.com/jobs?q=full+stack+developer&l=Hyderabad after 3 attempts\n2025-05-28 22:58:45,151 - WARNING - Failed to get response from Indeed for search 3\n2025-05-28 22:58:45,152 - INFO - Indeed.com results: 0 unique jobs\n2025-05-28 22:58:45,152 - INFO - SUCCESS Indeed: 0 jobs\n2025-05-28 22:58:45,152 - INFO - Total scraped from all sources: 60 jobs\n2025-05-28 22:58:45,153 - INFO - Stage 2: Applying multi-stage filtering...\n2025-05-28 22:58:45,153 - DEBUG - \\u2705 KEEPING 'Junior Software Engineer (Fresher-Friendly)' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:45,153 - DEBUG - \\u2705 KEEPING 'Cyber Security Analyst' - posted 11 hours ago (within 24 hours)\n2025-05-28 22:58:45,154 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 5 hours ago (within 24 hours)\n2025-05-28 22:58:45,154 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,154 - DEBUG - \\u2705 KEEPING 'Developer' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,155 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,155 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,155 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,155 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,155 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,156 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,156 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,156 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,156 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,156 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,157 - DEBUG - \\u2705 KEEPING 'Full Stack Developer' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,157 - DEBUG - \\u2705 KEEPING 'Automation Test Engineer' - posted 6 hours ago (within 24 hours)\n2025-05-28 22:58:45,157 - DEBUG - \\u2705 KEEPING 'Python Developer' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:45,157 - DEBUG - \\u2705 KEEPING 'Test Engineer - L4' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:45,157 - DEBUG - \\u2705 KEEPING 'Developer - L2' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:45,158 - DEBUG - \\u2705 KEEPING 'Software Engineer' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:45,158 - DEBUG - \\u2705 KEEPING 'AI Engineer' - posted 4 hours ago (within 24 hours)\n2025-05-28 22:58:45,158 - DEBUG - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,158 - DEBUG - \\u2705 KEEPING 'Software Developer' - posted 5 hours ago (within 24 hours)\n2025-05-28 22:58:45,158 - DEBUG - \\u2705 KEEPING 'Embedded Linux Field Engineer for Devices/IoT' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,159 - DEBUG - \\u2705 KEEPING 'Software Developer - Back End' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:45,159 - DEBUG - \\u2705 KEEPING 'Test Engineer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:45,159 - DEBUG - \\u2705 KEEPING 'Embedded Autosar' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,159 - DEBUG - \\u2705 KEEPING 'Test Engineer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:45,159 - DEBUG - \\u2705 KEEPING 'Engineer Software Development - II (Google CCAI)' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,160 - DEBUG - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,160 - DEBUG - \\u2705 KEEPING 'Full Stack Developer' - posted 5 hours ago (within 24 hours)\n2025-05-28 22:58:45,160 - DEBUG - \\u2705 KEEPING 'Associate Linux Support Engineer' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,160 - DEBUG - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,160 - DEBUG - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,160 - DEBUG - \\u2705 KEEPING 'DevOps Engineer - L4' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,161 - DEBUG - \\u2705 KEEPING 'IE Engineer I' - posted 3 hours ago (within 24 hours)\n2025-05-28 22:58:45,161 - DEBUG - \\u2705 KEEPING 'Configurator - L4' - posted 23 hours ago (within 24 hours)\n2025-05-28 22:58:45,161 - DEBUG - \\u2705 KEEPING 'Software Developer - Five9 Developer' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:45,161 - DEBUG - \\u2705 KEEPING 'Full Stack Developer' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:45,161 - DEBUG - \\u2705 KEEPING 'Developer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:45,162 - DEBUG - \\u2705 KEEPING 'Software Fullstack Developer - CMS' - posted 8 hours ago (within 24 hours)\n2025-05-28 22:58:45,162 - DEBUG - \\u2705 KEEPING 'Embedded Linux Consultant - Japan' - posted 22 hours ago (within 24 hours)\n2025-05-28 22:58:45,162 - DEBUG - \\u2705 KEEPING 'Data Platform Engineer (HPC Applications)' - posted 21 hours ago (within 24 hours)\n2025-05-28 22:58:45,162 - DEBUG - \\u2705 KEEPING 'Software Engineer - React Native' - posted 17 hours ago (within 24 hours)\n2025-05-28 22:58:45,162 - DEBUG - \\u2705 KEEPING 'React with PHP (any framework) Developer' - posted 4 hours ago (within 24 hours)\n2025-05-28 22:58:45,163 - DEBUG - \\u2705 KEEPING 'IN-Software Engineer .Net, ASP.Net Core, Angular, ReactJS' - posted 16 hours ago (within 24 hours)\n2025-05-28 22:58:45,163 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,163 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,163 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,163 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,164 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,164 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Developer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,164 - DEBUG - \\u2705 KEEPING 'Developer - L3' - posted 12 hours ago (within 24 hours)\n2025-05-28 22:58:45,164 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,164 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,165 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,165 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,165 - DEBUG - \\u2705 KEEPING 'Remote Fullstack Engineer - 17853' - posted 2 hours ago (within 24 hours)\n2025-05-28 22:58:45,165 - DEBUG - \\u2705 KEEPING 'Developer - L3' - posted 10 hours ago (within 24 hours)\n2025-05-28 22:58:45,166 - INFO - Date filter results (24h window): 60/60 jobs are recent\n2025-05-28 22:58:45,166 - INFO -   Within 24h window: 60 jobs\n2025-05-28 22:58:45,166 - INFO -   Promoted jobs (kept): 0 jobs\n2025-05-28 22:58:45,166 - INFO -   No date info (kept): 0 jobs\n2025-05-28 22:58:45,166 - INFO -   Excluded old postings: 0 jobs\n2025-05-28 22:58:45,167 - DEBUG - \\u2705 CSE/IT match: 'junior software engineer (fresher-friendly)' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,167 - DEBUG - \\u2705 CSE/IT match: 'cyber security analyst' - matched keywords: ['it', 'security analyst', 'cyber security']\n2025-05-28 22:58:45,167 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,168 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,168 - DEBUG - \\u2705 CSE/IT match: 'developer' - matched keywords: ['developer']\n2025-05-28 22:58:45,168 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,168 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,169 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,169 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,169 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,170 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,170 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,170 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,170 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,171 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,171 - DEBUG - \\u2705 CSE/IT match: 'full stack developer' - matched keywords: ['full stack developer', 'full stack', 'developer']\n2025-05-28 22:58:45,171 - DEBUG - \\u2705 CSE/IT match: 'automation test engineer' - matched keywords: ['engineer']\n2025-05-28 22:58:45,171 - DEBUG - \\u2705 CSE/IT match: 'python developer' - matched keywords: ['python developer', 'developer']\n2025-05-28 22:58:45,172 - DEBUG - \\u2705 CSE/IT match: 'test engineer - l4' - matched keywords: ['engineer']\n2025-05-28 22:58:45,172 - DEBUG - \\u2705 CSE/IT match: 'developer - l2' - matched keywords: ['developer']\n2025-05-28 22:58:45,172 - DEBUG - \\u2705 CSE/IT match: 'software engineer' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,173 - DEBUG - \\u2705 CSE/IT match: 'ai engineer' - matched keywords: ['ai engineer', 'engineer']\n2025-05-28 22:58:45,173 - DEBUG - \\u2705 CSE/IT match: 'devops engineer - l4' - matched keywords: ['devops engineer', 'devops', 'engineer']\n2025-05-28 22:58:45,173 - DEBUG - \\u2705 CSE/IT match: 'software developer' - matched keywords: ['software developer', 'developer']\n2025-05-28 22:58:45,173 - DEBUG - \\u2705 CSE/IT match: 'embedded linux field engineer for devices/iot' - matched keywords: ['engineer']\n2025-05-28 22:58:45,174 - DEBUG - \\u2705 CSE/IT match: 'software developer - back end' - matched keywords: ['software developer', 'developer']\n2025-05-28 22:58:45,174 - DEBUG - \\u2705 CSE/IT match: 'test engineer - l3' - matched keywords: ['engineer']\n2025-05-28 22:58:45,174 - DEBUG - \\u274c No field match: 'embedded autosar' - combined text: 'embedded autosar cognizant'\n2025-05-28 22:58:45,174 - DEBUG - \\u2705 CSE/IT match: 'test engineer - l3' - matched keywords: ['engineer']\n2025-05-28 22:58:45,174 - DEBUG - \\u2705 CSE/IT match: 'engineer software development - ii (google ccai)' - matched keywords: ['software development', 'development', 'engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'devops engineer - l4' - matched keywords: ['devops engineer', 'devops', 'engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'full stack developer' - matched keywords: ['full stack developer', 'full stack', 'developer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'associate linux support engineer' - matched keywords: ['engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'devops engineer - l4' - matched keywords: ['devops engineer', 'devops', 'engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'devops engineer - l4' - matched keywords: ['devops engineer', 'devops', 'engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'devops engineer - l4' - matched keywords: ['devops engineer', 'devops', 'engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u2705 CSE/IT match: 'ie engineer i' - matched keywords: ['engineer']\n2025-05-28 22:58:45,175 - DEBUG - \\u274c No field match: 'configurator - l4' - combined text: 'configurator - l4 wipro'\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'software developer - five9 developer' - matched keywords: ['software developer', 'developer']\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'full stack developer' - matched keywords: ['full stack developer', 'full stack', 'developer']\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'developer - l3' - matched keywords: ['developer']\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'software fullstack developer - cms' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,176 - DEBUG - \\u274c No field match: 'embedded linux consultant - japan' - combined text: 'embedded linux consultant - japan canonical'\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'data platform engineer (hpc applications)' - matched keywords: ['engineer']\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'software engineer - react native' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'react with php (any framework) developer' - matched keywords: ['it', 'developer']\n2025-05-28 22:58:45,176 - DEBUG - \\u2705 CSE/IT match: 'in-software engineer .net, asp.net core, angular, reactjs' - matched keywords: ['software engineer', 'engineer', 'software engineer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack developer - 17853' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack developer - 17853' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack developer - 17853' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack developer - 17853' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack developer - 17853' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack developer - 17853' - matched keywords: ['fullstack developer', 'fullstack', 'developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'developer - l3' - matched keywords: ['developer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack engineer - 17853' - matched keywords: ['fullstack', 'engineer']\n2025-05-28 22:58:45,177 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack engineer - 17853' - matched keywords: ['fullstack', 'engineer']\n2025-05-28 22:58:45,178 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack engineer - 17853' - matched keywords: ['fullstack', 'engineer']\n2025-05-28 22:58:45,178 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack engineer - 17853' - matched keywords: ['fullstack', 'engineer']\n2025-05-28 22:58:45,178 - DEBUG - \\u2705 CSE/IT match: 'remote fullstack engineer - 17853' - matched keywords: ['fullstack', 'engineer']\n2025-05-28 22:58:45,178 - DEBUG - \\u2705 CSE/IT match: 'developer - l3' - matched keywords: ['developer']\n2025-05-28 22:58:45,178 - INFO - Branch filter results: 57/60 jobs match field criteria\n2025-05-28 22:58:45,178 - INFO -   CSE/IT: 57 jobs\n2025-05-28 22:58:45,178 - INFO -   EEE/ECE: 0 jobs\n2025-05-28 22:58:45,178 - INFO -   MECH: 0 jobs\n2025-05-28 22:58:45,178 - DEBUG - \\u2705 Entry-level match: 'junior software engineer (fresher-friendly)' - matched keywords: ['fresher', 'junior', 'junior software']\n2025-05-28 22:58:45,178 - DEBUG - \\u2705 Assumed entry-level: 'cyber security analyst' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'developer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,179 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'full stack developer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'automation test engineer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'python developer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'test engineer - l4' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'developer - l2' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'software engineer' - no experience requirements found\n2025-05-28 22:58:45,180 - DEBUG - \\u2705 Assumed entry-level: 'ai engineer' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'devops engineer - l4' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'software developer' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'embedded linux field engineer for devices/iot' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'software developer - back end' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'test engineer - l3' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'test engineer - l3' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'engineer software development - ii (google ccai)' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'devops engineer - l4' - no experience requirements found\n2025-05-28 22:58:45,181 - DEBUG - \\u2705 Assumed entry-level: 'full stack developer' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Entry-level match: 'associate linux support engineer' - matched keywords: ['associate']\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'devops engineer - l4' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'devops engineer - l4' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'devops engineer - l4' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'ie engineer i' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'software developer - five9 developer' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'full stack developer' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'developer - l3' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'software fullstack developer - cms' - no experience requirements found\n2025-05-28 22:58:45,182 - DEBUG - \\u2705 Assumed entry-level: 'data platform engineer (hpc applications)' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'software engineer - react native' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'react with php (any framework) developer' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'in-software engineer .net, asp.net core, angular, reactjs' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack developer - 17853' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack developer - 17853' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack developer - 17853' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack developer - 17853' - no experience requirements found\n2025-05-28 22:58:45,183 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack developer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack developer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'developer - l3' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack engineer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack engineer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack engineer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack engineer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'remote fullstack engineer - 17853' - no experience requirements found\n2025-05-28 22:58:45,184 - DEBUG - \\u2705 Assumed entry-level: 'developer - l3' - no experience requirements found\n2025-05-28 22:58:45,184 - INFO - Experience filter results: 57/57 jobs are entry-level friendly\n2025-05-28 22:58:45,184 - INFO -   Explicit entry-level: 2 jobs\n2025-05-28 22:58:45,184 - INFO -   Assumed entry-level: 55 jobs\n2025-05-28 22:58:45,185 - INFO -   Excluded senior positions: 0 jobs\n2025-05-28 22:58:45,185 - DEBUG - Skipping previously seen job: Junior Software Engineer (Fresher-Friendly) at Nova\n2025-05-28 22:58:45,185 - DEBUG - Skipping previously seen job: Software Engineer at Capgemini\n2025-05-28 22:58:45,187 - INFO - Deduplication: 55/57 unique jobs (filtered out 2 duplicates/seen jobs)\n2025-05-28 22:58:45,187 - INFO - Stage 3: AI-powered job selection...\n2025-05-28 22:58:45,569 - INFO - AI analyzing 55 jobs...\n2025-05-28 22:58:45,623 - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-a03f9115-272f-487d-9a9f-87906c45a825', 'json_data': {'messages': [{'role': 'user', 'content': 'You are a career advisor for fresh engineering graduates (0-1 years experience). Analyze these entry-level job opportunities and select the SINGLE most valuable and legitimate one.\\n\\nEvaluation Criteria (in order of importance):\\n1. Entry-level friendliness (0-1 years experience, training provided, fresh graduate positions)\\n2. Relevance to engineering graduates (especially CSE/IT/AI/ML)\\n3. Company legitimacy and reputation\\n4. Career growth potential and learning opportunities for beginners\\n5. Technical skill development potential for new graduates\\n6. Industry standing and future prospects\\n\\nPRIORITIZE jobs with:\\n- Explicit entry-level indicators (junior, associate, fresh graduate, entry level)\\n- Training and mentorship opportunities\\n- No high experience requirements\\n- Clear growth paths for beginners\\n\\nJobs to evaluate:\\n[\\n  {\\n    \"number\": 1,\\n    \"title\": \"Cyber Security Analyst\",\\n    \"company\": \"Prudent Technologies and Consulting, Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 2,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 3,\\n    \"title\": \"Developer\",\\n    \"company\": \"Cognizant\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 4,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 5,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 6,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 7,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 8,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 9,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 10,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 11,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 12,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 13,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"JIVA InfoTech Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 14,\\n    \"title\": \"Full Stack Developer\",\\n    \"company\": \"STAND 8 Technology Consulting\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 15,\\n    \"title\": \"Automation Test Engineer\",\\n    \"company\": \"Resolver, a Kroll Business\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 16,\\n    \"title\": \"Python Developer\",\\n    \"company\": \"Algonox Technologies\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 17,\\n    \"title\": \"Test Engineer - L4\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 18,\\n    \"title\": \"Developer - L2\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 19,\\n    \"title\": \"Software Engineer\",\\n    \"company\": \"Zoetis\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 20,\\n    \"title\": \"AI Engineer\",\\n    \"company\": \"Anblicks\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 21,\\n    \"title\": \"DevOps Engineer - L4\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 22,\\n    \"title\": \"Software Developer\",\\n    \"company\": \"Zoetis\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 23,\\n    \"title\": \"Embedded Linux Field Engineer for Devices/IoT\",\\n    \"company\": \"Canonical\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 24,\\n    \"title\": \"Software Developer - Back End\",\\n    \"company\": \"Zoetis\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 25,\\n    \"title\": \"Test Engineer - L3\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 26,\\n    \"title\": \"Test Engineer - L3\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 27,\\n    \"title\": \"Engineer Software Development - II (Google CCAI)\",\\n    \"company\": \"Verizon\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 28,\\n    \"title\": \"DevOps Engineer - L4\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 29,\\n    \"title\": \"Full Stack Developer\",\\n    \"company\": \"Hummingbird Scientific\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 30,\\n    \"title\": \"Associate Linux Support Engineer\",\\n    \"company\": \"Canonical\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Explicit)\",\\n    \"experience_matches\": [\\n      \"associate\"\\n    ]\\n  },\\n  {\\n    \"number\": 31,\\n    \"title\": \"DevOps Engineer - L4\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 32,\\n    \"title\": \"DevOps Engineer - L4\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 33,\\n    \"title\": \"DevOps Engineer - L4\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 34,\\n    \"title\": \"IE Engineer I\",\\n    \"company\": \"RealPage, Inc.\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 35,\\n    \"title\": \"Software Developer - Five9 Developer\",\\n    \"company\": \"Zoetis\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 36,\\n    \"title\": \"Full Stack Developer\",\\n    \"company\": \"Zoetis\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 37,\\n    \"title\": \"Developer - L3\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 38,\\n    \"title\": \"Software Fullstack Developer - CMS\",\\n    \"company\": \"Zoetis\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 39,\\n    \"title\": \"Data Platform Engineer (HPC Applications)\",\\n    \"company\": \"Sanofi\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 40,\\n    \"title\": \"Software Engineer - React Native\",\\n    \"company\": \"Optum\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 41,\\n    \"title\": \"React with PHP (any framework) Developer\",\\n    \"company\": \"Talentifyy\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 42,\\n    \"title\": \"IN-Software Engineer .Net, ASP.Net Core, Angular, ReactJS\",\\n    \"company\": \"Blue Yonder\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 43,\\n    \"title\": \"Remote Fullstack Developer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 44,\\n    \"title\": \"Remote Fullstack Developer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 45,\\n    \"title\": \"Remote Fullstack Developer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 46,\\n    \"title\": \"Remote Fullstack Developer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 47,\\n    \"title\": \"Remote Fullstack Developer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 48,\\n    \"title\": \"Remote Fullstack Developer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 49,\\n    \"title\": \"Developer - L3\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 50,\\n    \"title\": \"Remote Fullstack Engineer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 51,\\n    \"title\": \"Remote Fullstack Engineer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 52,\\n    \"title\": \"Remote Fullstack Engineer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 53,\\n    \"title\": \"Remote Fullstack Engineer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 54,\\n    \"title\": \"Remote Fullstack Engineer - 17853\",\\n    \"company\": \"Turing\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  },\\n  {\\n    \"number\": 55,\\n    \"title\": \"Developer - L3\",\\n    \"company\": \"Wipro\",\\n    \"field_category\": \"CSE/IT\",\\n    \"priority_score\": 3,\\n    \"experience_level\": \"Entry Level (Assumed)\",\\n    \"experience_matches\": []\\n  }\\n]\\n\\nReturn only the exact title of the selected job. If all seem suspicious or low-quality, return \"NONE\".'}], 'model': 'llama3-8b-8192', 'max_tokens': 100, 'temperature': 0.1}}\n2025-05-28 22:58:45,690 - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions\n2025-05-28 22:58:45,690 - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None\n2025-05-28 22:58:45,868 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002625F160AD0>\n2025-05-28 22:58:45,869 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002625ED3F9B0> server_hostname='api.groq.com' timeout=5.0\n2025-05-28 22:58:46,032 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002625ED21A90>\n2025-05-28 22:58:46,033 - DEBUG - send_request_headers.started request=<Request [b'POST']>\n2025-05-28 22:58:46,034 - DEBUG - send_request_headers.complete\n2025-05-28 22:58:46,034 - DEBUG - send_request_body.started request=<Request [b'POST']>\n2025-05-28 22:58:46,035 - DEBUG - send_request_body.complete\n2025-05-28 22:58:46,035 - DEBUG - receive_response_headers.started request=<Request [b'POST']>\n2025-05-28 22:58:46,336 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 403, b'Forbidden', [(b'Date', b'Wed, 28 May 2025 17:28:45 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'__cf_bm=mU6ky8zl57Wg8NzyHWNfBxSz5JuORgW8dB4ckH7Nzs4-1748453325-*******-jOz7K6zq2wOf7M3i6eZjits535mqAWkd9lQVysKDqgc0uLfjOuM8fiGn.fNjbBs.F4LGP1CVrgab8q82Z2jnbyEjADzFcTu2aBG9zjzuNb0; path=/; expires=Wed, 28-May-25 17:58:45 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Vary', b'Accept-Encoding'), (b'Server', b'cloudflare'), (b'CF-RAY', b'946f78653ba0d486-NRT'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=\":443\"; ma=86400')])\n2025-05-28 22:58:46,337 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions \"HTTP/1.1 403 Forbidden\"\n2025-05-28 22:58:46,338 - DEBUG - receive_response_body.started request=<Request [b'POST']>\n2025-05-28 22:58:46,535 - DEBUG - receive_response_body.complete\n2025-05-28 22:58:46,535 - DEBUG - response_closed.started\n2025-05-28 22:58:46,536 - DEBUG - response_closed.complete\n2025-05-28 22:58:46,536 - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions \"403 Forbidden\" Headers({'date': 'Wed, 28 May 2025 17:28:45 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'set-cookie': '__cf_bm=mU6ky8zl57Wg8NzyHWNfBxSz5JuORgW8dB4ckH7Nzs4-1748453325-*******-jOz7K6zq2wOf7M3i6eZjits535mqAWkd9lQVysKDqgc0uLfjOuM8fiGn.fNjbBs.F4LGP1CVrgab8q82Z2jnbyEjADzFcTu2aBG9zjzuNb0; path=/; expires=Wed, 28-May-25 17:58:45 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'vary': 'Accept-Encoding', 'server': 'cloudflare', 'cf-ray': '946f78653ba0d486-NRT', 'content-encoding': 'gzip', 'alt-svc': 'h3=\":443\"; ma=86400'})\n2025-05-28 22:58:46,537 - DEBUG - Encountered httpx.HTTPStatusError\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\.venv\\Lib\\site-packages\\groq\\_base_client.py\", line 1011, in request\n    response.raise_for_status()\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\.venv\\Lib\\site-packages\\httpx\\_models.py\", line 829, in raise_for_status\n    raise HTTPStatusError(message, request=request, response=self)\nhttpx.HTTPStatusError: Client error '403 Forbidden' for url 'https://api.groq.com/openai/v1/chat/completions'\nFor more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/403\n2025-05-28 22:58:46,541 - DEBUG - Not retrying\n2025-05-28 22:58:46,541 - DEBUG - Re-raising status error\n2025-05-28 22:58:46,541 - ERROR - AI selection failed: Error code: 403 - {'error': {'message': 'Access denied. Please check your network settings.'}}\n2025-05-28 22:58:46,543 - DEBUG - Saved job signature: Cyber Security Analyst-Prudent Technologies and Consulting, Inc.-https://in.linkedin.com/jobs/view/cyber-security-analyst-at-prudent-technologies-and-consulting-inc-4231635147\n2025-05-28 22:58:46,544 - INFO - Stage 4: Saving results...\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\jobs_scraper.py\", line 1544, in <module>\n    display_results(result)\n    ~~~~~~~~~~~~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\jobs_scraper.py\", line 1452, in display_results\n    print(\"\\U0001f3af RECENT ENTRY-LEVEL JOB SCRAPER (24H + 0-1 YEARS)\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3af' in position 0: character maps to <undefined>\n2025-05-28 22:58:46,593 - DEBUG - close.started\n2025-05-28 22:58:46,593 - DEBUG - close.complete\n", "timeout": false}, {"script": "demo_upskill.py", "success": false, "return_code": 1, "execution_time": 1.3480114936828613, "stdout": "=== Upskill Articles Scraper for CS Students ===\nThis scraper finds educational content to help computer science students\nlearn new technologies, best practices, and implementation techniques.\n\n", "stderr": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\demo_upskill.py\", line 143, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\demo_upskill.py\", line 16, in main\n    print(\"\\U0001f3af TARGET CONTENT:\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3af' in position 0: character maps to <undefined>\n", "timeout": false}, {"script": "daily_tech_aggregator.py", "success": false, "return_code": 1, "execution_time": 0.7507076263427734, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\daily_tech_aggregator.py\", line 455, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\tji-auto\\daily_tech_aggregator.py\", line 430, in main\n    print(\"\\U0001f504 DAILY TECH DIGEST AGGREGATOR\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f504' in position 0: character maps to <undefined>\n", "timeout": false}], "file_status": {"ai_selected_article.json": {"exists": false, "size": 0}, "selected_internship.json": {"exists": false, "size": 0}, "selected_job.json": {"exists": true, "size": 509, "valid_json": true}, "ai_selected_upskill_article.json": {"exists": false, "size": 0}, "daily_tech_digest.json": {"exists": false, "size": 0}}}